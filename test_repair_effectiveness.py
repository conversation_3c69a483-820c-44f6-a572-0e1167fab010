#!/usr/bin/env python3
"""
Test script to show exact repair effectiveness for different car conditions.
Shows why minor repairs might not be visible on cars already at 99% condition.
"""

import json
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from valuation_system import valuation_system
from maintenance_system import maintenance_system

def test_repair_effectiveness_on_current_car():
    """Test repair effectiveness on the current car in garage"""
    print("=== Test Skuteczności Napraw na Twoim <PERSON> ===")
    
    try:
        # Load current car data
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)
        
        with open('data/profile.json', 'r') as f:
            profile_data = json.load(f)
        
        if not garage_data:
            print("❌ Brak danych samochodu")
            return False
        
        car_data = garage_data[0]  # First car
        car_index = 0
        car_key = str(car_index)
        
        # Get current usage data
        usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(car_key,
                                                                            valuation_system.get_default_usage_data())
        
        print(f"🚗 Samochód: {car_data.get('name', '<PERSON><PERSON>nany')}")
        print(f"💰 Twoje pieniądze: {profile_data.get('money', 0)}$")
        print()
        
        # Show current detailed condition
        print("📊 AKTUALNY STAN SZCZEGÓŁOWY:")
        part_types = ["car", "engine", "turbo", "intercooler", "ecu"]
        part_names = {
            "car": "Samochód",
            "engine": "Silnik", 
            "turbo": "Turbo",
            "intercooler": "Intercooler",
            "ecu": "ECU"
        }
        
        current_conditions = {}
        for part_type in part_types:
            age_key = f"{part_type}_age_days"
            age_days = usage_data.get(age_key, usage_data.get("car_age_days", 0))
            races = usage_data.get("races_completed", 0)
            
            condition = valuation_system.calculate_condition(age_days, races, part_type)
            current_conditions[part_type] = condition
            print(f"   {part_names[part_type]}: {condition:.4f} ({condition*100:.2f}%)")
        
        # Calculate average condition
        avg_condition = sum(current_conditions.values()) / len(current_conditions)
        print(f"\n📈 ŚREDNI STAN: {avg_condition:.4f} ({avg_condition*100:.2f}%)")
        
        # Show current usage data
        print(f"\n🔧 DANE ZUŻYCIA:")
        print(f"   Wiek samochodu: {usage_data.get('car_age_days', 0):.1f} dni")
        print(f"   Ukończone wyścigi: {usage_data.get('races_completed', 0)}")
        for part_type in ["engine", "turbo", "intercooler", "ecu"]:
            age_key = f"{part_type}_age_days"
            age = usage_data.get(age_key, 0)
            print(f"   Wiek {part_names[part_type].lower()}: {age:.1f} dni")
        
        # Get repair options
        repair_options = maintenance_system.get_repair_options(car_index)
        print(f"\n💸 OPCJE NAPRAW:")
        
        for option in repair_options:
            print(f"\n🔧 {option['name']} - {option['cost']}$")
            print(f"   Opis: {option['description']}")
            
            # Simulate this repair
            repair_type = option['type']
            if repair_type == "minor":
                age_reduction = 0.15
            elif repair_type == "major":
                age_reduction = 0.4
            elif repair_type == "full":
                age_reduction = 0.7
            
            # Simulate repair effects
            simulated_usage = usage_data.copy()
            
            # Apply age reduction
            for part_type in part_types:
                age_key = f"{part_type}_age_days"
                if age_key in simulated_usage:
                    current_age = simulated_usage[age_key]
                    simulated_usage[age_key] = max(0, current_age * (1 - age_reduction))
            
            # For full repair, also reduce races
            if repair_type == "full":
                current_races = simulated_usage.get("races_completed", 0)
                simulated_usage["races_completed"] = max(0, int(current_races * 0.8))
            
            # Calculate new conditions
            print(f"   📊 STAN PO NAPRAWIE:")
            new_conditions = {}
            for part_type in part_types:
                age_key = f"{part_type}_age_days"
                age_days = simulated_usage.get(age_key, simulated_usage.get("car_age_days", 0))
                races = simulated_usage.get("races_completed", 0)
                
                new_condition = valuation_system.calculate_condition(age_days, races, part_type)
                new_conditions[part_type] = new_condition
                
                improvement = new_condition - current_conditions[part_type]
                print(f"      {part_names[part_type]}: {new_condition:.4f} ({new_condition*100:.2f}%) [+{improvement*100:.2f}%]")
            
            # Calculate new average
            new_avg = sum(new_conditions.values()) / len(new_conditions)
            avg_improvement = new_avg - avg_condition
            print(f"   📈 NOWY ŚREDNI STAN: {new_avg:.4f} ({new_avg*100:.2f}%) [+{avg_improvement*100:.2f}%]")
            
            # Show if improvement is visible
            if avg_improvement * 100 < 0.5:
                print(f"   ⚠️  UWAGA: Poprawa może być niewidoczna w interfejsie (mniej niż 0.5%)")
            elif avg_improvement * 100 < 1.0:
                print(f"   ℹ️  Poprawa będzie ledwo widoczna w interfejsie")
            else:
                print(f"   ✅ Poprawa będzie wyraźnie widoczna w interfejsie")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def explain_repair_mechanics():
    """Explain how repair mechanics work"""
    print("\n" + "="*60)
    print("📚 JAK DZIAŁAJĄ NAPRAWY - WYJAŚNIENIE")
    print("="*60)
    
    print("""
🔧 RODZAJE NAPRAW:

1. 🛠️  DROBNA NAPRAWA (5% wartości samochodu):
   • Redukuje wiek wszystkich części o 15%
   • Najlepsze dla samochodów w dobrym stanie (80%+)
   • Może dać małą poprawę na samochodach już w bardzo dobrym stanie

2. 🔧 GŁÓWNA NAPRAWA (15% wartości samochodu):
   • Redukuje wiek wszystkich części o 40%
   • Dobra dla samochodów w średnim stanie (50-80%)
   • Znacząca poprawa stanu

3. 🏭 PEŁNA RENOWACJA (30% wartości samochodu):
   • Redukuje wiek wszystkich części o 70%
   • Redukuje liczbę ukończonych wyścigów o 20%
   • Najlepsza dla bardzo zużytych samochodów
   • Może przywrócić samochód do stanu bliskiego nowemu

💡 DLACZEGO DROBNA NAPRAWA MOŻE NIE POMÓC:

Jeśli Twój samochód ma już 99% stanu, oznacza to że:
• Części są już w bardzo dobrym stanie
• Redukcja wieku o 15% może dać poprawę o 0.1-0.5%
• W interfejsie może to być niewidoczne (zaokrąglenie)

🎯 REKOMENDACJE:

• Samochód 95%+: Drobna naprawa (jeśli chcesz perfekcję)
• Samochód 70-95%: Główna naprawa (najlepszy stosunek ceny do efektu)
• Samochód <70%: Pełna renowacja (przywróci do doskonałego stanu)

💰 EKONOMIA:

• Nie naprawiaj samochodów powyżej 95% - to marnotrawstwo
• Lepiej zainwestuj w nowe części lub lepszy samochód
• Naprawy są najbardziej opłacalne dla bardzo zużytych samochodów
""")

def main():
    """Run repair effectiveness test"""
    print("🔧 Test Skuteczności Napraw - Analiza Twojego Samochodu")
    print("=" * 60)
    
    success = test_repair_effectiveness_on_current_car()
    
    if success:
        explain_repair_mechanics()
        print("\n" + "="*60)
        print("✅ Analiza zakończona!")
        print("💡 Teraz wiesz dlaczego drobna naprawa mogła nie pomóc")
    else:
        print("❌ Nie udało się przeprowadzić analizy")

if __name__ == "__main__":
    main()
