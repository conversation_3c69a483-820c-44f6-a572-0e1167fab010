"""
Language Manager for Xtra Cars Game
Handles loading and managing translations for multiple languages.
"""

import json
import os
import locale
from typing import Dict, Any, Optional
from utils import resource_path


def detect_system_language() -> str:
    """
    Detect system language and return appropriate language code.
    Returns 'en' as fallback if system language is not supported.
    """
    try:
        # Get system locale
        system_locale = locale.getdefaultlocale()[0]
        if system_locale:
            # Extract language code (first 2 characters)
            lang_code = system_locale[:2].lower()

            # Map to supported languages
            supported_languages = {"pl", "en", "de", "es"}
            if lang_code in supported_languages:
                return lang_code

        # Fallback to English
        return "en"

    except Exception as e:
        print(f"Error detecting system language: {e}")
        return "en"  # Safe fallback


class LanguageManager:
    """Manages game translations and language switching"""
    
    def __init__(self):
        # Detect system language, fallback to English
        self.current_language = detect_system_language()
        self.translations: Dict[str, Dict[str, Any]] = {}
        self.available_languages = {
            "pl": "Polski",
            "en": "English",
            "de": "Deutsch",
            "es": "Español"
        }
        self.load_all_languages()
        
    def load_all_languages(self):
        """Load all available language files"""
        for lang_code in self.available_languages.keys():
            self.load_language(lang_code)
    
    def load_language(self, language_code: str) -> bool:
        """Load a specific language file"""
        try:
            language_file = f"data/languages/{language_code}.json"
            with open(resource_path(language_file), 'r', encoding='utf-8') as f:
                self.translations[language_code] = json.load(f)
            return True
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Error loading language {language_code}: {e}")
            return False
    
    def set_language(self, language_code: str) -> bool:
        """Set the current language"""
        if language_code in self.available_languages:
            self.current_language = language_code
            return True
        return False
    
    def get_text(self, key_path: str, **kwargs) -> str:
        """
        Get translated text by key path (e.g., 'menu.main_menu')
        Supports string formatting with kwargs
        """
        try:
            # Get current language translations
            current_translations = self.translations.get(self.current_language, {})
            
            # Navigate through nested keys
            keys = key_path.split('.')
            text = current_translations
            
            for key in keys:
                if isinstance(text, dict) and key in text:
                    text = text[key]
                else:
                    # Fallback to English if key not found in current language
                    if self.current_language != "en":
                        english_translations = self.translations.get("en", {})
                        text = english_translations
                        for fallback_key in keys:
                            if isinstance(text, dict) and fallback_key in text:
                                text = text[fallback_key]
                            else:
                                return f"[MISSING: {key_path}]"
                    else:
                        return f"[MISSING: {key_path}]"
                    break
            
            # If we got a string, format it with kwargs
            if isinstance(text, str):
                try:
                    return text.format(**kwargs)
                except (KeyError, ValueError):
                    return text
            else:
                return f"[INVALID: {key_path}]"
                
        except Exception as e:
            print(f"Error getting text for key '{key_path}': {e}")
            return f"[ERROR: {key_path}]"
    
    def get_available_languages(self) -> Dict[str, str]:
        """Get dictionary of available languages {code: name}"""
        return self.available_languages.copy()
    
    def get_current_language(self) -> str:
        """Get current language code"""
        return self.current_language
    
    def get_current_language_name(self) -> str:
        """Get current language display name"""
        return self.available_languages.get(self.current_language, "Unknown")
    
    def save_language_preference(self, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """Save language preference to profile data"""
        if "settings" not in profile_data:
            profile_data["settings"] = {}
        profile_data["settings"]["language"] = self.current_language
        return profile_data
    
    def load_language_preference(self, profile_data: Dict[str, Any]) -> bool:
        """Load language preference from profile data"""
        try:
            settings = profile_data.get("settings", {})
            # If no saved language, use system language detection
            saved_language = settings.get("language", detect_system_language())
            return self.set_language(saved_language)
        except Exception as e:
            print(f"Error loading language preference: {e}")
            return False


# Global language manager instance
language_manager = None


def get_language_manager() -> LanguageManager:
    """Get the global language manager instance"""
    global language_manager
    if language_manager is None:
        language_manager = LanguageManager()
    return language_manager


def init_language_manager() -> LanguageManager:
    """Initialize the global language manager"""
    global language_manager
    language_manager = LanguageManager()
    return language_manager


def get_text(key_path: str, **kwargs) -> str:
    """Convenience function to get translated text"""
    return get_language_manager().get_text(key_path, **kwargs)


def set_language(language_code: str) -> bool:
    """Convenience function to set language"""
    return get_language_manager().set_language(language_code)


def get_current_language() -> str:
    """Convenience function to get current language"""
    return get_language_manager().get_current_language()


def get_available_languages() -> Dict[str, str]:
    """Convenience function to get available languages"""
    return get_language_manager().get_available_languages()
