"""
Test script for resolution selection fixes.
This script tests the specific issues that were reported:
1. Random resolution selection bug
2. Fullscreen mode not working
3. Incorrect active resolution display
"""

import pygame
import sys
import time
from game import Game


def test_resolution_button_actions():
    """Test that resolution buttons select the correct resolution"""
    print("Testing resolution button actions...")
    
    pygame.init()
    pygame.font.init()
    
    # Create a game instance
    game = Game()
    
    # Test each resolution
    test_resolutions = [
        (1024, 768, "1024x768"),
        (1280, 720, "1280x720 (HD)"),
        (1366, 768, "1366x768"),
        (1920, 1080, "1920x1080 (Full HD)"),
        (2560, 1440, "2560x1440 (QHD)"),
        ("fullscreen", "fullscreen", "Pełny ekran")
    ]
    
    for width, height, name in test_resolutions:
        print(f"\nTesting resolution change to: {name}")
        
        # Store current state
        prev_width = game.s_width
        prev_height = game.s_height
        prev_fullscreen = game.is_fullscreen
        
        try:
            # Call change_resolution directly
            game.change_resolution(width, height)
            
            # Check if the resolution was set correctly
            if width == "fullscreen":
                if game.is_fullscreen:
                    print(f"  ✓ Fullscreen mode: PASS")
                    print(f"    Screen size: {game.s_width}x{game.s_height}")
                    print(f"    Screen flags: {game.screen.get_flags()}")
                    print(f"    Fullscreen flag: {game.screen.get_flags() & pygame.FULLSCREEN}")
                else:
                    print(f"  ✗ Fullscreen mode: FAIL - is_fullscreen is False")
            else:
                if not game.is_fullscreen and game.s_width == width and game.s_height == height:
                    print(f"  ✓ Windowed resolution {width}x{height}: PASS")
                else:
                    print(f"  ✗ Windowed resolution {width}x{height}: FAIL")
                    print(f"    Expected: {width}x{height}, fullscreen=False")
                    print(f"    Actual: {game.s_width}x{game.s_height}, fullscreen={game.is_fullscreen}")
                    
        except Exception as e:
            print(f"  ✗ Resolution change failed: {e}")
        
        # Brief pause
        time.sleep(0.1)
    
    pygame.quit()
    print("\nResolution button action testing completed.")


def test_active_resolution_detection():
    """Test that the active resolution is correctly detected and marked"""
    print("\nTesting active resolution detection...")
    
    pygame.init()
    pygame.font.init()
    
    # Create a game instance
    game = Game()
    
    # Test resolutions list (same as in game)
    resolutions = [
        (1024, 768, "1024x768"),
        (1280, 720, "1280x720 (HD)"),
        (1366, 768, "1366x768"),
        (1920, 1080, "1920x1080 (Full HD)"),
        (2560, 1440, "2560x1440 (QHD)"),
        ("fullscreen", "fullscreen", "Pełny ekran")
    ]
    
    # Test different states
    test_states = [
        (1024, 768, False, "1024x768 windowed"),
        (1920, 1080, False, "1920x1080 windowed"),
        (0, 0, True, "fullscreen")  # 0,0 represents fullscreen
    ]
    
    for test_width, test_height, test_fullscreen, description in test_states:
        print(f"\nTesting active detection for: {description}")
        
        # Set the game state
        if test_fullscreen:
            game.screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
            game.is_fullscreen = True
        else:
            game.screen = pygame.display.set_mode((test_width, test_height))
            game.is_fullscreen = False
        
        game.s_width, game.s_height = game.screen.get_size()
        
        print(f"  Game state: {game.s_width}x{game.s_height}, fullscreen={game.is_fullscreen}")
        
        # Test the active detection logic for each resolution option
        active_count = 0
        for width, height, label in resolutions:
            if width == "fullscreen":
                is_current = game.is_fullscreen
            else:
                is_current = (not game.is_fullscreen and 
                            game.s_width == width and 
                            game.s_height == height)
            
            if is_current:
                active_count += 1
                print(f"    ✓ {label} marked as active")
        
        if active_count == 1:
            print(f"  ✓ Active detection: PASS (exactly 1 option marked active)")
        elif active_count == 0:
            print(f"  ✗ Active detection: FAIL (no option marked active)")
        else:
            print(f"  ✗ Active detection: FAIL ({active_count} options marked active)")
    
    pygame.quit()
    print("\nActive resolution detection testing completed.")


def test_fullscreen_transitions():
    """Test fullscreen mode transitions specifically"""
    print("\nTesting fullscreen transitions...")
    
    pygame.init()
    pygame.font.init()
    
    # Create a game instance
    game = Game()
    
    try:
        # Start in windowed mode
        print("Setting windowed mode 1024x768...")
        game.change_resolution(1024, 768)
        print(f"  Result: {game.s_width}x{game.s_height}, fullscreen={game.is_fullscreen}")
        print(f"  Screen flags: {game.screen.get_flags()}")
        
        if not game.is_fullscreen and game.s_width == 1024 and game.s_height == 768:
            print("  ✓ Windowed mode: PASS")
        else:
            print("  ✗ Windowed mode: FAIL")
        
        time.sleep(0.5)
        
        # Switch to fullscreen
        print("\nSwitching to fullscreen...")
        game.change_resolution("fullscreen", "fullscreen")
        print(f"  Result: {game.s_width}x{game.s_height}, fullscreen={game.is_fullscreen}")
        print(f"  Screen flags: {game.screen.get_flags()}")
        print(f"  Fullscreen flag check: {game.screen.get_flags() & pygame.FULLSCREEN}")
        
        if game.is_fullscreen:
            print("  ✓ Fullscreen mode: PASS")
        else:
            print("  ✗ Fullscreen mode: FAIL")
        
        time.sleep(0.5)
        
        # Switch back to windowed
        print("\nSwitching back to windowed 1280x720...")
        game.change_resolution(1280, 720)
        print(f"  Result: {game.s_width}x{game.s_height}, fullscreen={game.is_fullscreen}")
        print(f"  Screen flags: {game.screen.get_flags()}")
        
        if not game.is_fullscreen and game.s_width == 1280 and game.s_height == 720:
            print("  ✓ Back to windowed: PASS")
        else:
            print("  ✗ Back to windowed: FAIL")
            
    except Exception as e:
        print(f"  ✗ Fullscreen transition test failed: {e}")
    
    pygame.quit()
    print("Fullscreen transition testing completed.")


if __name__ == "__main__":
    print("=== Resolution Selection Fixes Test Suite ===")
    
    try:
        test_resolution_button_actions()
        test_active_resolution_detection()
        test_fullscreen_transitions()
        
        print("\n=== Test Summary ===")
        print("All resolution selection fix tests completed.")
        print("Check the output above for any FAIL messages.")
        print("If all tests show PASS, the resolution selection fixes are working correctly.")
        
    except Exception as e:
        print(f"Test suite failed with error: {e}")
        sys.exit(1)
