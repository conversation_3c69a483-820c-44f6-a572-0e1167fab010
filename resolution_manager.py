"""
Resolution Manager for handling UI scaling and positioning across different screen resolutions.
"""

import pygame
from typing import Tuple, List, Dict, Any
from responsive_ui import ResponsiveLayout


class ResolutionManager:
    """Manages resolution changes and UI element repositioning"""
    
    def __init__(self, initial_width: int, initial_height: int):
        self.current_width = initial_width
        self.current_height = initial_height
        self.base_width = 1920  # Reference resolution width
        self.base_height = 1080  # Reference resolution height
        self.responsive_ui = ResponsiveLayout(initial_width, initial_height)
        
        # Store UI elements that need repositioning
        self.ui_elements = []
        
    def register_ui_element(self, element: Any, position_type: str = "center", 
                          base_x: int = 0, base_y: int = 0, 
                          offset_x: int = 0, offset_y: int = 0):
        """
        Register a UI element for automatic repositioning
        
        Args:
            element: The UI element (button, text, etc.)
            position_type: "center", "top_left", "top_right", "bottom_left", "bottom_right", "relative"
            base_x, base_y: Base position for relative positioning
            offset_x, offset_y: Offset from the base position
        """
        ui_info = {
            'element': element,
            'position_type': position_type,
            'base_x': base_x,
            'base_y': base_y,
            'offset_x': offset_x,
            'offset_y': offset_y,
            'original_width': getattr(element, 'width', 0),
            'original_height': getattr(element, 'height', 0)
        }
        self.ui_elements.append(ui_info)
        
    def update_resolution(self, new_width: int, new_height: int):
        """Update resolution and reposition all registered UI elements"""
        self.current_width = new_width
        self.current_height = new_height
        self.responsive_ui = ResponsiveLayout(new_width, new_height)
        
        # Reposition all registered UI elements
        self._reposition_ui_elements()
        
    def _reposition_ui_elements(self):
        """Reposition all registered UI elements based on new resolution"""
        for ui_info in self.ui_elements:
            element = ui_info['element']
            position_type = ui_info['position_type']
            
            if position_type == "center":
                # Center the element on screen
                if hasattr(element, 'rect'):
                    element.rect.centerx = self.current_width // 2
                    element.rect.centery = self.current_height // 2
                elif hasattr(element, 'x') and hasattr(element, 'y'):
                    element.x = self.current_width // 2 - (ui_info['original_width'] // 2)
                    element.y = self.current_height // 2 - (ui_info['original_height'] // 2)
                    
            elif position_type == "relative":
                # Position relative to screen size with scaling
                scale_x = self.current_width / self.base_width
                scale_y = self.current_height / self.base_height
                
                new_x = int((ui_info['base_x'] + ui_info['offset_x']) * scale_x)
                new_y = int((ui_info['base_y'] + ui_info['offset_y']) * scale_y)
                
                if hasattr(element, 'rect'):
                    element.rect.x = new_x
                    element.rect.y = new_y
                elif hasattr(element, 'x') and hasattr(element, 'y'):
                    element.x = new_x
                    element.y = new_y
                    
            elif position_type == "top_left":
                if hasattr(element, 'rect'):
                    element.rect.x = ui_info['offset_x']
                    element.rect.y = ui_info['offset_y']
                elif hasattr(element, 'x') and hasattr(element, 'y'):
                    element.x = ui_info['offset_x']
                    element.y = ui_info['offset_y']
                    
            elif position_type == "top_right":
                if hasattr(element, 'rect'):
                    element.rect.x = self.current_width - ui_info['original_width'] - ui_info['offset_x']
                    element.rect.y = ui_info['offset_y']
                elif hasattr(element, 'x') and hasattr(element, 'y'):
                    element.x = self.current_width - ui_info['original_width'] - ui_info['offset_x']
                    element.y = ui_info['offset_y']
                    
            elif position_type == "bottom_left":
                if hasattr(element, 'rect'):
                    element.rect.x = ui_info['offset_x']
                    element.rect.y = self.current_height - ui_info['original_height'] - ui_info['offset_y']
                elif hasattr(element, 'x') and hasattr(element, 'y'):
                    element.x = ui_info['offset_x']
                    element.y = self.current_height - ui_info['original_height'] - ui_info['offset_y']
                    
            elif position_type == "bottom_right":
                if hasattr(element, 'rect'):
                    element.rect.x = self.current_width - ui_info['original_width'] - ui_info['offset_x']
                    element.rect.y = self.current_height - ui_info['original_height'] - ui_info['offset_y']
                elif hasattr(element, 'x') and hasattr(element, 'y'):
                    element.x = self.current_width - ui_info['original_width'] - ui_info['offset_x']
                    element.y = self.current_height - ui_info['original_height'] - ui_info['offset_y']
    
    def clear_ui_elements(self):
        """Clear all registered UI elements"""
        self.ui_elements.clear()
        
    def get_scaled_font_size(self, base_size: int) -> int:
        """Get scaled font size for current resolution"""
        return self.responsive_ui.scale_font_size(base_size)
        
    def get_scaled_spacing(self, base_spacing: int) -> int:
        """Get scaled spacing for current resolution"""
        return self.responsive_ui.scale_spacing(base_spacing)
        
    def get_scaled_button_size(self, base_width: int, base_height: int) -> Tuple[int, int]:
        """Get scaled button size for current resolution"""
        return self.responsive_ui.scale_button_size(base_width, base_height)
        
    def get_center_position(self, element_width: int, element_height: int) -> Tuple[int, int]:
        """Get center position for an element"""
        x = (self.current_width - element_width) // 2
        y = (self.current_height - element_height) // 2
        return x, y
        
    def get_safe_area(self, margin: int = 50) -> Tuple[int, int, int, int]:
        """
        Get safe area coordinates (x, y, width, height) with margin from screen edges
        Useful for ensuring UI elements don't go off-screen
        """
        return (margin, margin, self.current_width - 2 * margin, self.current_height - 2 * margin)
        
    def clamp_to_screen(self, x: int, y: int, width: int, height: int, margin: int = 10) -> Tuple[int, int]:
        """
        Clamp position to ensure element stays within screen boundaries
        
        Args:
            x, y: Current position
            width, height: Element dimensions
            margin: Minimum distance from screen edges
            
        Returns:
            Tuple of clamped (x, y) coordinates
        """
        max_x = self.current_width - width - margin
        max_y = self.current_height - height - margin
        
        clamped_x = max(margin, min(x, max_x))
        clamped_y = max(margin, min(y, max_y))
        
        return clamped_x, clamped_y
        
    def scale_coordinate(self, x: int, y: int, from_width: int, from_height: int) -> Tuple[int, int]:
        """
        Scale coordinates from one resolution to current resolution
        
        Args:
            x, y: Original coordinates
            from_width, from_height: Original resolution
            
        Returns:
            Scaled coordinates for current resolution
        """
        scale_x = self.current_width / from_width
        scale_y = self.current_height / from_height
        
        new_x = int(x * scale_x)
        new_y = int(y * scale_y)
        
        return new_x, new_y


# Global resolution manager instance
resolution_manager = None

def get_resolution_manager() -> ResolutionManager:
    """Get the global resolution manager instance"""
    global resolution_manager
    if resolution_manager is None:
        # Initialize with default resolution
        resolution_manager = ResolutionManager(1920, 1080)
    return resolution_manager

def init_resolution_manager(width: int, height: int):
    """Initialize the global resolution manager with specific dimensions"""
    global resolution_manager
    resolution_manager = ResolutionManager(width, height)
