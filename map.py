import pygame
from utils import load_image

class Map:
    def __init__(self, name, screen_width, screen_height):
        self.x_cord = 0
        self.y_cord = 0
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.original_img = load_image(f'./assets/img/maps/{name}.png')

        # Store original dimensions for proper scaling calculations
        self.original_width = self.original_img.get_width()
        self.original_height = self.original_img.get_height()

        # Scale the map to fit the screen properly
        self._scale_map()

    def _scale_map(self):
        """Scale the map image to fit the screen height and extend width for scrolling"""
        # Scale height to match screen height
        self.height = self.screen_height

        # Calculate width maintaining aspect ratio, then extend for scrolling
        aspect_ratio = self.original_width / self.original_height
        base_width = int(self.height * aspect_ratio)

        # Extend width for scrolling (2.5x for gameplay)
        self.width = base_width
        extended_width = int(base_width * 2.5)

        # Scale the image to the new dimensions
        self.img_scaled = pygame.transform.scale(self.original_img, (extended_width, self.height))

    def update_resolution(self, screen_width, screen_height):
        """Update map when resolution changes"""
        self.screen_width = screen_width
        self.screen_height = screen_height
        self._scale_map()

    def tick(self, speed):
        self.x_cord -= speed

    def draw(self, screen):
        screen.blit(self.img_scaled, (self.x_cord, self.y_cord))

    def is_map_ended(self, map_distance):
        return -self.x_cord >= map_distance