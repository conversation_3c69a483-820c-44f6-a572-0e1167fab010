import pygame
import json
from background import Background
from ui_components import TextButton
from maintenance_system import maintenance_system
from valuation_system import valuation_system
from utils import load_font
from cursor_manager import cursor_manager

# Global variables for debouncing
_maintenance_last_click_time = 0
_repair_last_click_times = {}

def draw_repair_screen(s_width, s_height, screen, car_index):
    """Draw the car repair screen"""
    run = True
    bg = Background('background', s_width, s_height)
    
    # Load car and profile data
    try:
        from utils import resource_path
        with open(resource_path('data/garage.json')) as f:
            cars_data = json.load(f)
        with open(resource_path('data/profile.json')) as f:
            profile_data = json.load(f)
    except Exception as e:
        print(f"Error loading data: {e}")
        return
    
    if car_index >= len(cars_data):
        return
    
    car_data = cars_data[car_index]
    
    # Get current usage data and condition
    usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(car_index), 
                                                                        valuation_system.get_default_usage_data())
    
    # Calculate current condition
    performance_data = valuation_system.calculate_enhanced_performance(car_data, usage_data)
    current_condition = performance_data["condition_effects"]["avg_parts_condition"]
    
    # Create buttons with responsive positioning
    back_button = TextButton('Powrót', 50, 50, font_size=36)

    # Position maintenance button below repair options
    maintenance_y = s_height - 120  # Safe distance from bottom
    maintenance_button = TextButton('Konserwacja', s_width // 2 - 100, maintenance_y, font_size=32, width=200, height=50)
    
    # Get repair options
    repair_options = maintenance_system.get_repair_options(car_index)
    repair_buttons = []

    # Calculate responsive positioning for repair buttons
    buttons_per_row = min(3, len(repair_options))  # Max 3 buttons per row
    button_width = 200
    button_height = 80
    button_spacing = 30

    # Calculate starting position to center buttons
    total_width = buttons_per_row * button_width + (buttons_per_row - 1) * button_spacing
    start_x = (s_width - total_width) // 2
    start_y = s_height // 2 + 100

    for i, option in enumerate(repair_options):
        row = i // buttons_per_row
        col = i % buttons_per_row
        x = start_x + col * (button_width + button_spacing)
        y = start_y + row * (button_height + button_spacing + 40)  # Extra space for descriptions
        button = TextButton(f"{option['name']}\n{option['cost']} $", x, y, font_size=24, width=button_width, height=button_height)
        repair_buttons.append((button, option))
    
    # Create fonts
    header_font = load_font("arial", 48)
    info_font = load_font("arial", 32)
    detail_font = load_font("arial", 24)
    
    # Message display
    message = ""
    message_color = (255, 255, 255)
    message_timer = 0
    
    while run:
        dt = pygame.time.Clock().tick(60) / 1000.0
        mouse_pos = pygame.mouse.get_pos()

        # Reset cursor state for this frame
        cursor_manager.reset_frame()

        # Update message timer
        if message_timer > 0:
            message_timer -= dt
            if message_timer <= 0:
                message = ""

        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                return

            # Handle button clicks with events
            if back_button.handle_event(event, mouse_pos):
                return

            if maintenance_button.handle_event(event, mouse_pos):
                global _maintenance_last_click_time
                current_time = pygame.time.get_ticks()

                if current_time - _maintenance_last_click_time > 500:  # 500ms debounce
                    _maintenance_last_click_time = current_time
                    success, msg = maintenance_system.perform_maintenance(car_index)
                    if success:
                        message = msg
                        message_color = (0, 255, 0)
                        message_timer = 3.0
                        # Reload data
                        with open('data/profile.json') as f:
                            profile_data = json.load(f)
                        usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(car_index),
                                                                                            valuation_system.get_default_usage_data())
                        performance_data = valuation_system.calculate_enhanced_performance(car_data, usage_data)
                        current_condition = performance_data["condition_effects"]["avg_parts_condition"]
                    else:
                        message = msg
                        message_color = (255, 0, 0)
                        message_timer = 3.0

            # Handle repair button clicks with events
            for button, option in repair_buttons:
                if button.handle_event(event, mouse_pos):
                    global _repair_last_click_times
                    current_time = pygame.time.get_ticks()
                    repair_key = f"repair_{option['type']}"

                    if repair_key not in _repair_last_click_times:
                        _repair_last_click_times[repair_key] = 0

                    if current_time - _repair_last_click_times[repair_key] > 600:  # 600ms debounce for repairs
                        _repair_last_click_times[repair_key] = current_time
                        success, msg = maintenance_system.repair_car(car_index, option['type'])
                        if success:
                            message = msg
                            message_color = (0, 255, 0)
                            message_timer = 3.0
                            # Reload data
                            with open('data/profile.json') as f:
                                profile_data = json.load(f)
                            usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(car_index),
                                                                                                valuation_system.get_default_usage_data())
                            performance_data = valuation_system.calculate_enhanced_performance(car_data, usage_data)
                            current_condition = performance_data["condition_effects"]["avg_parts_condition"]
                        else:
                            message = msg
                            message_color = (255, 0, 0)
                            message_timer = 3.0

        # Update button hover states
        back_button.update(mouse_pos)
        maintenance_button.update(mouse_pos)
        for button, option in repair_buttons:
            button.update(mouse_pos)

        
        # Draw everything
        bg.draw(screen)
        
        # Draw header
        header = header_font.render("Warsztat Samochodowy", True, (255, 255, 255))
        screen.blit(header, (s_width // 2 - header.get_width() // 2, 50))
        
        # Draw car info
        car_name = info_font.render(f"Samochód: {car_data['name'].title()}", True, (255, 255, 255))
        screen.blit(car_name, (100, 150))
        
        # Draw condition info
        condition_percent = int(current_condition * 100)
        if condition_percent >= 80:
            condition_color = (0, 255, 0)  # Green
        elif condition_percent >= 60:
            condition_color = (255, 255, 0)  # Yellow
        elif condition_percent >= 40:
            condition_color = (255, 165, 0)  # Orange
        else:
            condition_color = (255, 0, 0)  # Red
        
        condition_text = info_font.render(f"Stan ogólny: {condition_percent}%", True, condition_color)
        screen.blit(condition_text, (100, 200))
        
        # Draw money
        money_text = info_font.render(f"Pieniądze: {profile_data['money']} $", True, (255, 255, 0))
        screen.blit(money_text, (s_width - money_text.get_width() - 100, 150))
        
        # Draw repair options header
        repair_header = info_font.render("Opcje naprawy:", True, (255, 255, 255))
        screen.blit(repair_header, (100, s_height // 2 + 50))
        
        # Draw repair option details with better positioning
        for i, (button, option) in enumerate(repair_buttons):
            button.draw(screen)

            # Draw option description below button with background for better visibility
            desc_y = button.rect.y + button.rect.height + 10
            desc_text = detail_font.render(option['description'], True, (200, 200, 200))

            # Create background for description
            desc_bg = pygame.Rect(button.rect.x - 5, desc_y - 2, desc_text.get_width() + 10, desc_text.get_height() + 4)
            pygame.draw.rect(screen, (0, 0, 0, 180), desc_bg)
            pygame.draw.rect(screen, (100, 100, 100), desc_bg, 1)
            screen.blit(desc_text, (button.rect.x, desc_y))

        # Draw maintenance button
        maintenance_button.draw(screen)

        # Draw maintenance info with background
        maint_info = detail_font.render("Konserwacja: Podstawowa poprawa stanu (-5 dni wieku)", True, (200, 200, 200))
        maint_info_y = maintenance_button.rect.y + maintenance_button.rect.height + 10
        maint_bg = pygame.Rect(maintenance_button.rect.x - 5, maint_info_y - 2, maint_info.get_width() + 10, maint_info.get_height() + 4)
        pygame.draw.rect(screen, (0, 0, 0, 180), maint_bg)
        pygame.draw.rect(screen, (100, 100, 100), maint_bg, 1)
        screen.blit(maint_info, (maintenance_button.rect.x, maint_info_y))
        
        # Draw buttons
        back_button.draw(screen)
        
        # Draw message if any with better positioning and background
        if message and message_timer > 0:
            msg_text = info_font.render(message, True, message_color)
            msg_x = s_width // 2 - msg_text.get_width() // 2
            msg_y = s_height - 30  # Closer to bottom edge

            # Create background for message
            msg_bg = pygame.Rect(msg_x - 10, msg_y - 5, msg_text.get_width() + 20, msg_text.get_height() + 10)
            pygame.draw.rect(screen, (0, 0, 0, 200), msg_bg)
            pygame.draw.rect(screen, message_color, msg_bg, 2)
            screen.blit(msg_text, (msg_x, msg_y))

        # Update cursor based on hover states
        cursor_manager.update_cursor()

        pygame.display.update()
