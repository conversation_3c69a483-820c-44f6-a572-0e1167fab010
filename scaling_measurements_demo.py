"""
Detailed measurements demonstration for main menu scaling.
Shows exact pixel measurements and scaling calculations.
"""

import pygame
import sys
from game import Game
from resolution_manager import get_resolution_manager, init_resolution_manager


def measure_scaling_at_resolution(width, height, resolution_name):
    """Get detailed measurements at specific resolution"""
    print(f"\n🔍 MEASURING SCALING AT {resolution_name}")
    print("=" * 50)
    
    try:
        pygame.init()
        pygame.font.init()
        
        # Initialize resolution manager
        init_resolution_manager(width, height)
        resolution_manager = get_resolution_manager()
        
        print(f"📐 Resolution Manager State:")
        print(f"  Current resolution: {resolution_manager.current_width}x{resolution_manager.current_height}")
        print(f"  Base resolution: {resolution_manager.base_width}x{resolution_manager.base_height}")
        
        # Calculate scaling factors
        scale_x = resolution_manager.current_width / resolution_manager.base_width
        scale_y = resolution_manager.current_height / resolution_manager.base_height
        print(f"  Scaling factors: X={scale_x:.3f}, Y={scale_y:.3f}")
        
        # Get scaled values
        measurements = {}
        
        # Button measurements
        base_button_font = 36
        base_button_width, base_button_height = 250, 60
        base_spacing = 75
        
        scaled_font = resolution_manager.get_scaled_font_size(base_button_font)
        scaled_button_width, scaled_button_height = resolution_manager.get_scaled_button_size(base_button_width, base_button_height)
        scaled_spacing = resolution_manager.get_scaled_spacing(base_spacing)
        
        measurements['button_font'] = {
            'base': base_button_font,
            'scaled': scaled_font,
            'ratio': scaled_font / base_button_font
        }
        
        measurements['button_width'] = {
            'base': base_button_width,
            'scaled': scaled_button_width,
            'ratio': scaled_button_width / base_button_width
        }
        
        measurements['button_height'] = {
            'base': base_button_height,
            'scaled': scaled_button_height,
            'ratio': scaled_button_height / base_button_height
        }
        
        measurements['spacing'] = {
            'base': base_spacing,
            'scaled': scaled_spacing,
            'ratio': scaled_spacing / base_spacing
        }
        
        # Title measurements
        base_title_font = 48
        scaled_title_font = resolution_manager.get_scaled_font_size(base_title_font)
        
        measurements['title_font'] = {
            'base': base_title_font,
            'scaled': scaled_title_font,
            'ratio': scaled_title_font / base_title_font
        }
        
        # Position calculations
        button_labels = ['Gra', 'Samochody', 'Handel', 'Ustawienia', 'Wyjdź z gry']
        total_buttons_height = len(button_labels) * scaled_button_height + (len(button_labels) - 1) * scaled_spacing
        title_space = resolution_manager.get_scaled_spacing(120)
        available_height = height - title_space
        start_y = title_space + (available_height - total_buttons_height) // 2
        button_x = (width - scaled_button_width) // 2
        
        measurements['positioning'] = {
            'total_buttons_height': total_buttons_height,
            'title_space': title_space,
            'available_height': available_height,
            'start_y': start_y,
            'button_x': button_x
        }
        
        # Print detailed measurements
        print(f"\n📏 DETAILED MEASUREMENTS:")
        print("-" * 30)
        
        for category, data in measurements.items():
            if category == 'positioning':
                print(f"\n🎯 {category.upper()}:")
                for key, value in data.items():
                    print(f"  {key}: {value}px")
            else:
                base_val = data['base']
                scaled_val = data['scaled']
                ratio = data['ratio']
                change = scaled_val - base_val
                change_percent = ((scaled_val / base_val) - 1) * 100
                
                print(f"\n📊 {category.upper()}:")
                print(f"  Base value: {base_val}px")
                print(f"  Scaled value: {scaled_val}px")
                print(f"  Change: {change:+.1f}px ({change_percent:+.1f}%)")
                print(f"  Scaling ratio: {ratio:.3f}x")
        
        pygame.quit()
        return measurements
        
    except Exception as e:
        print(f"❌ Error measuring {resolution_name}: {e}")
        pygame.quit()
        return None


def compare_scaling_measurements():
    """Compare measurements across different resolutions"""
    print("🎯 SCALING MEASUREMENTS COMPARISON")
    print("=" * 50)
    
    test_resolutions = [
        (1024, 768, "Small (1024x768)"),
        (1366, 768, "Medium (1366x768)"),
        (1920, 1080, "Large (1920x1080)")
    ]
    
    all_measurements = {}
    
    for width, height, name in test_resolutions:
        measurements = measure_scaling_at_resolution(width, height, name)
        if measurements:
            all_measurements[name] = measurements
    
    # Create comparison table
    if len(all_measurements) >= 2:
        print(f"\n📊 SCALING COMPARISON TABLE")
        print("=" * 80)
        
        # Header
        resolutions = list(all_measurements.keys())
        print(f"{'Measurement':<20}", end="")
        for res in resolutions:
            print(f"{res:<20}", end="")
        print()
        print("-" * (20 + 20 * len(resolutions)))
        
        # Button font sizes
        print(f"{'Button Font Size':<20}", end="")
        for res in resolutions:
            font_size = all_measurements[res]['button_font']['scaled']
            print(f"{font_size}px{'':<16}", end="")
        print()
        
        # Button dimensions
        print(f"{'Button Width':<20}", end="")
        for res in resolutions:
            width = all_measurements[res]['button_width']['scaled']
            print(f"{width}px{'':<16}", end="")
        print()
        
        print(f"{'Button Height':<20}", end="")
        for res in resolutions:
            height = all_measurements[res]['button_height']['scaled']
            print(f"{height}px{'':<16}", end="")
        print()
        
        # Spacing
        print(f"{'Button Spacing':<20}", end="")
        for res in resolutions:
            spacing = all_measurements[res]['spacing']['scaled']
            print(f"{spacing}px{'':<16}", end="")
        print()
        
        # Title font
        print(f"{'Title Font Size':<20}", end="")
        for res in resolutions:
            title_font = all_measurements[res]['title_font']['scaled']
            print(f"{title_font}px{'':<16}", end="")
        print()
        
        # Calculate differences
        small_res = list(all_measurements.values())[0]
        large_res = list(all_measurements.values())[-1]
        
        print(f"\n📈 SCALING DIFFERENCES (Large vs Small):")
        print("-" * 40)
        
        categories = ['button_font', 'button_width', 'button_height', 'spacing', 'title_font']
        for category in categories:
            small_val = small_res[category]['scaled']
            large_val = large_res[category]['scaled']
            difference = large_val - small_val
            percent_change = ((large_val / small_val) - 1) * 100
            
            print(f"{category.replace('_', ' ').title()}: {small_val}px → {large_val}px ({difference:+.0f}px, {percent_change:+.1f}%)")
    
    return all_measurements


def verify_resolution_manager_execution():
    """Verify that resolution manager is actually being used"""
    print(f"\n🔧 VERIFYING RESOLUTION MANAGER EXECUTION")
    print("=" * 50)
    
    try:
        # Test resolution manager import and initialization
        from resolution_manager import get_resolution_manager, init_resolution_manager
        print("✅ Resolution manager import: SUCCESS")
        
        # Initialize with test resolution
        init_resolution_manager(1920, 1080)
        rm = get_resolution_manager()
        print("✅ Resolution manager initialization: SUCCESS")
        print(f"   Current state: {rm.current_width}x{rm.current_height}")
        
        # Test scaling functions
        test_font = rm.get_scaled_font_size(36)
        test_button = rm.get_scaled_button_size(250, 60)
        test_spacing = rm.get_scaled_spacing(75)
        
        print("✅ Scaling functions: SUCCESS")
        print(f"   Font scaling: 36 → {test_font}")
        print(f"   Button scaling: (250, 60) → {test_button}")
        print(f"   Spacing scaling: 75 → {test_spacing}")
        
        # Test with different resolution
        init_resolution_manager(1024, 768)
        rm = get_resolution_manager()
        
        test_font_small = rm.get_scaled_font_size(36)
        test_button_small = rm.get_scaled_button_size(250, 60)
        test_spacing_small = rm.get_scaled_spacing(75)
        
        print("✅ Resolution change: SUCCESS")
        print(f"   New state: {rm.current_width}x{rm.current_height}")
        print(f"   Font scaling: 36 → {test_font_small}")
        print(f"   Button scaling: (250, 60) → {test_button_small}")
        print(f"   Spacing scaling: 75 → {test_spacing_small}")
        
        # Verify scaling differences
        if test_font != test_font_small:
            print("✅ Scaling variation: CONFIRMED")
            print(f"   Font size changes: {test_font_small} (small) vs {test_font} (large)")
        else:
            print("⚠️  Scaling variation: NOT DETECTED")
            
        return True
        
    except Exception as e:
        print(f"❌ Resolution manager verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🔬 MAIN MENU SCALING MEASUREMENTS TOOL")
    print("=" * 45)
    
    try:
        # Verify resolution manager is working
        rm_working = verify_resolution_manager_execution()
        
        if rm_working:
            # Run detailed measurements
            measurements = compare_scaling_measurements()
            
            print(f"\n💡 WHAT THIS MEANS:")
            print("-" * 20)
            print("✅ If you see DIFFERENT numbers across resolutions, scaling is WORKING")
            print("❌ If you see IDENTICAL numbers across resolutions, scaling is NOT working")
            print("🎯 Larger resolutions should have LARGER font sizes and button dimensions")
            
        else:
            print("❌ Resolution manager is not working properly!")
            print("   The main menu scaling fixes may not be active.")
        
    except Exception as e:
        print(f"❌ Error during measurement: {e}")
        import traceback
        traceback.print_exc()
