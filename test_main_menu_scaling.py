"""
Test script for main menu scaling functionality.
This script tests the main menu UI scaling across different resolutions.
"""

import pygame
import sys
import time
from game import Game


def test_main_menu_scaling():
    """Test main menu scaling at different resolutions"""
    print("Testing main menu scaling...")
    
    pygame.init()
    pygame.font.init()
    
    # Test resolutions
    test_resolutions = [
        (1024, 768, "1024x768"),
        (1280, 720, "1280x720 (HD)"),
        (1366, 768, "1366x768"),
        (1920, 1080, "1920x1080 (Full HD)"),
        (2560, 1440, "2560x1440 (QHD)")
    ]
    
    for width, height, name in test_resolutions:
        print(f"\nTesting main menu scaling at: {name}")
        
        try:
            # Create a game instance and set resolution
            game = Game()
            game.change_resolution(width, height)
            
            print(f"  Screen size: {game.s_width}x{game.s_height}")
            
            # Test resolution manager integration
            try:
                from resolution_manager import get_resolution_manager
                resolution_manager = get_resolution_manager()
                
                # Test scaling values
                button_font_size = resolution_manager.get_scaled_font_size(36)
                button_width, button_height = resolution_manager.get_scaled_button_size(250, 60)
                button_spacing = resolution_manager.get_scaled_spacing(75)
                title_font_size = resolution_manager.get_scaled_font_size(48)
                title_space = resolution_manager.get_scaled_spacing(120)
                
                print(f"  Button font size: {button_font_size}")
                print(f"  Button size: {button_width}x{button_height}")
                print(f"  Button spacing: {button_spacing}")
                print(f"  Title font size: {title_font_size}")
                print(f"  Title space: {title_space}")
                
                # Test button positioning
                button_labels = ['Gra', 'Samochody', 'Handel', 'Ustawienia', 'Wyjdź z gry']
                total_buttons_height = len(button_labels) * button_height + (len(button_labels) - 1) * button_spacing
                available_height = game.s_height - title_space
                start_y = title_space + (available_height - total_buttons_height) // 2
                
                print(f"  Total buttons height: {total_buttons_height}")
                print(f"  Available height: {available_height}")
                print(f"  Start Y position: {start_y}")
                
                # Check if buttons fit within screen
                last_button_y = start_y + (len(button_labels) - 1) * (button_height + button_spacing) + button_height
                if last_button_y <= game.s_height:
                    print("  ✓ Buttons fit within screen: PASS")
                else:
                    print(f"  ✗ Buttons exceed screen: FAIL (last button at {last_button_y}, screen height {game.s_height})")
                
                # Check if buttons are properly centered
                button_x = (game.s_width - button_width) // 2
                if button_x >= 0 and button_x + button_width <= game.s_width:
                    print("  ✓ Buttons properly centered: PASS")
                else:
                    print(f"  ✗ Buttons not properly centered: FAIL (x={button_x}, width={button_width})")
                
                # Check title positioning
                title_x = (game.s_width - 200) // 2  # Approximate title width
                title_y = resolution_manager.get_scaled_spacing(50)
                if title_x >= 0 and title_y >= 0:
                    print("  ✓ Title positioning: PASS")
                else:
                    print(f"  ✗ Title positioning: FAIL (x={title_x}, y={title_y})")
                
                print("  ✓ Resolution manager integration: PASS")
                
            except Exception as e:
                print(f"  ✗ Resolution manager test failed: {e}")
            
            # Brief pause
            time.sleep(0.1)
            
        except Exception as e:
            print(f"  ✗ Main menu scaling test failed: {e}")
    
    pygame.quit()
    print("\nMain menu scaling testing completed.")


def test_main_menu_visual():
    """Visual test of main menu scaling - opens actual main menu for inspection"""
    print("\nStarting visual main menu test...")
    print("This will open the main menu at different resolutions for visual inspection.")
    print("Press ESC or close the window to continue to the next resolution.")
    
    pygame.init()
    pygame.font.init()
    
    # Test resolutions for visual inspection
    test_resolutions = [
        (1024, 768, "1024x768"),
        (1280, 720, "1280x720 (HD)"),
        (1920, 1080, "1920x1080 (Full HD)")
    ]
    
    for width, height, name in test_resolutions:
        print(f"\nVisual test at {name} - Press ESC to continue...")
        
        try:
            # Create game and set resolution
            game = Game()
            game.change_resolution(width, height)
            
            # Create a simplified main menu for testing
            from resolution_manager import get_resolution_manager
            resolution_manager = get_resolution_manager()
            
            # Get responsive scaling values
            button_font_size = resolution_manager.get_scaled_font_size(36)
            button_width, button_height = resolution_manager.get_scaled_button_size(250, 60)
            button_spacing = resolution_manager.get_scaled_spacing(75)
            title_font_size = resolution_manager.get_scaled_font_size(48)
            
            # Create test buttons
            from ui_components import TextButton
            from background import Background
            from utils import load_font
            from cursor_manager import cursor_manager
            
            button_labels = ['Gra', 'Samochody', 'Handel', 'Ustawienia', 'Wyjdź z gry']
            buttons = []
            
            # Calculate positions
            total_buttons_height = len(button_labels) * button_height + (len(button_labels) - 1) * button_spacing
            title_space = resolution_manager.get_scaled_spacing(120)
            available_height = game.s_height - title_space
            start_y = title_space + (available_height - total_buttons_height) // 2
            
            for i, label in enumerate(button_labels):
                button_x = (game.s_width - button_width) // 2
                button_y = start_y + i * (button_height + button_spacing)
                
                button = TextButton(label, button_x, button_y, 
                                  font_size=button_font_size, 
                                  width=button_width, height=button_height,
                                  action=lambda: None)
                buttons.append(button)
            
            background = Background('background', game.s_width, game.s_height)
            
            # Simple event loop for visual testing
            clock = pygame.time.Clock()
            running = True
            
            while running:
                for event in pygame.event.get():
                    if event.type == pygame.QUIT:
                        running = False
                    if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                        running = False
                
                mouse_pos = pygame.mouse.get_pos()
                cursor_manager.reset_frame()
                
                background.draw(game.screen)
                
                # Draw title
                title_font = load_font("arial", title_font_size)
                title = title_font.render("Xtreme Cars", True, (255, 255, 255))
                title_y = resolution_manager.get_scaled_spacing(50)
                title_x = (game.s_width - title.get_width()) // 2
                game.screen.blit(title, (title_x, title_y))
                
                # Draw buttons
                for button in buttons:
                    button.update(mouse_pos)
                    button.draw(game.screen)
                
                cursor_manager.update_cursor()
                pygame.display.update()
                clock.tick(60)
            
        except Exception as e:
            print(f"  ✗ Visual test failed: {e}")
    
    pygame.quit()
    print("Visual main menu testing completed.")


if __name__ == "__main__":
    print("=== Main Menu Scaling Test Suite ===")
    
    try:
        test_main_menu_scaling()
        
        # Ask user if they want visual testing
        response = input("\nDo you want to run visual tests? (y/n): ").lower().strip()
        if response == 'y' or response == 'yes':
            test_main_menu_visual()
        
        print("\n=== Test Summary ===")
        print("Main menu scaling tests completed.")
        print("Check the output above for any FAIL messages.")
        print("If all tests show PASS, the main menu scaling is working correctly.")
        
    except Exception as e:
        print(f"Test suite failed with error: {e}")
        sys.exit(1)
