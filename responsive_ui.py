"""
Responsive UI positioning system for Xtreme Cars game.
Provides utilities for creating responsive layouts that work across different screen resolutions.
"""

import pygame
from typing import Tuple, Dict, List, Optional

class ResponsiveLayout:
    """
    A class to handle responsive UI positioning based on screen dimensions.
    """
    
    def __init__(self, screen_width: int, screen_height: int):
        self.screen_width = screen_width
        self.screen_height = screen_height
        
        # Define breakpoints for different screen sizes
        self.breakpoints = {
            'small': 1024,    # Small screens (1024x768 and below)
            'medium': 1366,   # Medium screens (1366x768)
            'large': 1920,    # Large screens (1920x1080)
            'xlarge': 2560    # Extra large screens (2560x1440 and above)
        }
        
        # Determine current screen category
        self.screen_category = self._get_screen_category()
        
        # Define scaling factors for different elements
        self.scaling_factors = self._get_scaling_factors()
    
    def _get_screen_category(self) -> str:
        """Determine the screen category based on width."""
        if self.screen_width <= self.breakpoints['small']:
            return 'small'
        elif self.screen_width <= self.breakpoints['medium']:
            return 'medium'
        elif self.screen_width <= self.breakpoints['large']:
            return 'large'
        else:
            return 'xlarge'
    
    def _get_scaling_factors(self) -> Dict[str, float]:
        """Get scaling factors for different UI elements based on screen category."""
        factors = {
            'small': {
                'font_scale': 0.8,
                'spacing_scale': 0.8,
                'button_scale': 0.9,
                'margin_scale': 0.7
            },
            'medium': {
                'font_scale': 0.9,
                'spacing_scale': 0.9,
                'button_scale': 0.95,
                'margin_scale': 0.8
            },
            'large': {
                'font_scale': 1.0,
                'spacing_scale': 1.0,
                'button_scale': 1.0,
                'margin_scale': 1.0
            },
            'xlarge': {
                'font_scale': 1.2,
                'spacing_scale': 1.1,
                'button_scale': 1.1,
                'margin_scale': 1.2
            }
        }
        return factors[self.screen_category]
    
    def scale_font_size(self, base_size: int) -> int:
        """Scale font size based on screen resolution."""
        return int(base_size * self.scaling_factors['font_scale'])
    
    def scale_spacing(self, base_spacing: int) -> int:
        """Scale spacing between elements based on screen resolution."""
        return int(base_spacing * self.scaling_factors['spacing_scale'])
    
    def scale_button_size(self, base_width: int, base_height: int) -> Tuple[int, int]:
        """Scale button dimensions based on screen resolution."""
        scale = self.scaling_factors['button_scale']
        return int(base_width * scale), int(base_height * scale)
    
    def get_margin(self, base_margin: int) -> int:
        """Get responsive margin based on screen resolution."""
        return int(base_margin * self.scaling_factors['margin_scale'])
    
    def center_horizontally(self, element_width: int) -> int:
        """Get x position to center an element horizontally."""
        return (self.screen_width - element_width) // 2
    
    def center_vertically(self, element_height: int) -> int:
        """Get y position to center an element vertically."""
        return (self.screen_height - element_height) // 2
    
    def get_grid_layout(self, items_count: int, max_per_row: int, 
                       item_width: int, item_height: int, 
                       spacing: int) -> List[Tuple[int, int]]:
        """
        Calculate positions for a grid layout of items.
        Returns list of (x, y) positions for each item.
        """
        positions = []
        
        # Adjust items per row based on screen size
        if self.screen_category == 'small':
            items_per_row = min(max_per_row - 1, items_count)
        else:
            items_per_row = min(max_per_row, items_count)
        
        if items_per_row <= 0:
            items_per_row = 1
        
        # Calculate total grid dimensions
        total_width = items_per_row * item_width + (items_per_row - 1) * spacing
        start_x = self.center_horizontally(total_width)
        
        # Calculate starting y position (leave space for header)
        header_space = self.scale_spacing(150)
        start_y = header_space
        
        for i in range(items_count):
            row = i // items_per_row
            col = i % items_per_row
            
            x = start_x + col * (item_width + spacing)
            y = start_y + row * (item_height + spacing)
            
            positions.append((x, y))
        
        return positions
    
    def get_safe_text_position(self, text_width: int, text_height: int, 
                              preferred_x: int, preferred_y: int) -> Tuple[int, int]:
        """
        Get a safe position for text that won't go off-screen.
        """
        margin = self.get_margin(20)
        
        # Ensure text doesn't go off the right edge
        safe_x = min(preferred_x, self.screen_width - text_width - margin)
        safe_x = max(safe_x, margin)
        
        # Ensure text doesn't go off the bottom edge
        safe_y = min(preferred_y, self.screen_height - text_height - margin)
        safe_y = max(safe_y, margin)
        
        return safe_x, safe_y
    
    def create_text_background(self, text_surface: pygame.Surface, 
                              x: int, y: int, 
                              padding: int = 10, 
                              bg_color: Tuple[int, int, int, int] = (0, 0, 0, 180),
                              border_color: Optional[Tuple[int, int, int]] = None) -> pygame.Rect:
        """
        Create a background rectangle for text with responsive padding.
        Returns the background rectangle.
        """
        responsive_padding = self.scale_spacing(padding)
        
        bg_rect = pygame.Rect(
            x - responsive_padding,
            y - responsive_padding,
            text_surface.get_width() + 2 * responsive_padding,
            text_surface.get_height() + 2 * responsive_padding
        )
        
        return bg_rect
    
    def get_button_layout(self, button_count: int, button_width: int, button_height: int,
                         screen_section: str = 'bottom') -> List[Tuple[int, int]]:
        """
        Get responsive layout for buttons.
        screen_section can be 'top', 'middle', 'bottom'
        """
        positions = []
        spacing = self.scale_spacing(20)
        margin = self.get_margin(50)
        
        # Calculate total width needed
        total_width = button_count * button_width + (button_count - 1) * spacing
        
        # If buttons don't fit, stack them vertically
        if total_width > self.screen_width - 2 * margin:
            # Vertical layout
            start_x = self.center_horizontally(button_width)
            
            if screen_section == 'top':
                start_y = margin
            elif screen_section == 'middle':
                total_height = button_count * button_height + (button_count - 1) * spacing
                start_y = self.center_vertically(total_height)
            else:  # bottom
                total_height = button_count * button_height + (button_count - 1) * spacing
                start_y = self.screen_height - total_height - margin
            
            for i in range(button_count):
                x = start_x
                y = start_y + i * (button_height + spacing)
                positions.append((x, y))
        else:
            # Horizontal layout
            start_x = self.center_horizontally(total_width)
            
            if screen_section == 'top':
                y = margin
            elif screen_section == 'middle':
                y = self.center_vertically(button_height)
            else:  # bottom
                y = self.screen_height - button_height - margin
            
            for i in range(button_count):
                x = start_x + i * (button_width + spacing)
                positions.append((x, y))
        
        return positions

def create_responsive_layout(screen_width: int, screen_height: int) -> ResponsiveLayout:
    """Factory function to create a ResponsiveLayout instance."""
    return ResponsiveLayout(screen_width, screen_height)

# Utility functions for common UI patterns
def draw_text_with_background(screen: pygame.Surface, text_surface: pygame.Surface,
                             x: int, y: int, layout: ResponsiveLayout,
                             bg_color: Tuple[int, int, int, int] = (0, 0, 0, 180),
                             border_color: Optional[Tuple[int, int, int]] = None,
                             border_width: int = 2) -> None:
    """Draw text with a responsive background."""
    bg_rect = layout.create_text_background(text_surface, x, y)
    
    # Draw background
    if len(bg_color) == 4:  # RGBA
        overlay = pygame.Surface((bg_rect.width, bg_rect.height))
        overlay.set_alpha(bg_color[3])
        overlay.fill(bg_color[:3])
        screen.blit(overlay, bg_rect)
    else:  # RGB
        pygame.draw.rect(screen, bg_color, bg_rect)
    
    # Draw border if specified
    if border_color:
        pygame.draw.rect(screen, border_color, bg_rect, border_width)
    
    # Draw text
    screen.blit(text_surface, (x, y))
