import pygame
import time
import json
from valuation_system import valuation_system
from parts_requirements import parts_requirements
from utils import load_font, resource_path, load_image

class BaseCar:
    def __init__(self, name, color, weight, parts, x_cord, y_cord, start_time, car_index=None):
        self.name = name
        # Load car image without color system - larger size for races
        try:
            image_path = f"assets/img/cars/{name}_car.png"
            self.img = load_image(image_path)
            self.img = pygame.transform.scale(self.img, (250, 150))
        except Exception as e:
            print(f"Error loading car image: {e}")
            self.img = pygame.Surface((250, 150))
            self.img.fill((100, 100, 100))
        self.car_index = car_index
        self.parts = parts
        self.base_weight = weight

        # Store car data for condition calculations
        self.car_data = {
            "name": name,
            "weight": weight,
            "parts": parts
        }

        # Calculate base performance (without condition effects)
        self.calculate_base_performance()

        # Apply condition-based performance if car_index is available
        self.update_performance_with_condition()

        # Initialize position and movement
        self.speed = 0
        self._pos = pygame.math.Vector2(x_cord, y_cord)
        self.x_cord = int(x_cord)
        self.y_cord = y_cord
        self.start_time = start_time

    def calculate_base_performance(self):
        """Calculate base performance without condition effects"""
        # Ensure car has all required parts before calculating performance
        car_data = {"parts": self.parts}
        is_valid, missing_parts, _ = parts_requirements.validate_car_parts(car_data)

        if not is_valid:
            # Fix missing parts automatically
            fixed_car_data, parts_added = parts_requirements.fix_car_parts(car_data, add_to_inventory=False)
            self.parts = fixed_car_data["parts"]
            if parts_added:
                print(f"Auto-fixed missing parts for {self.name}: {parts_added}")

        # Calculate total weight using safe method
        self.total_weight = parts_requirements.safe_calculate_weight(self.base_weight, self.parts)

        # Calculate horsepower using safe method
        self.base_horsepower = parts_requirements.safe_calculate_horsepower(self.parts)

        # Store base performance values
        ratio = self.base_horsepower / self.total_weight if self.total_weight > 0 else 0
        self.base_max_speed = 300 + ratio * 2000
        self.base_acceleration = 100 + ratio * 1000

    def update_performance_with_condition(self):
        """Update performance based on current car condition"""
        if self.car_index is not None:
            try:
                # Get current usage data
                with open(resource_path('data/profile.json'), 'r', encoding='utf-8') as f:
                    profile_data = json.load(f)

                usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(self.car_index),
                                                                                   valuation_system.get_default_usage_data())

                # Calculate enhanced performance with condition effects
                performance_data = valuation_system.calculate_enhanced_performance(self.car_data, usage_data)

                # Apply condition-based performance penalties
                condition_factor = performance_data["condition_effects"]["avg_parts_condition"]

                # Performance scales with condition (minimum 60% performance at worst condition)
                performance_multiplier = 0.6 + (0.4 * condition_factor)

                # Update actual performance values
                self.horsepower = int(performance_data["total_horsepower"])
                self.max_speed = self.base_max_speed * performance_multiplier
                self.acceleration = self.base_acceleration * performance_multiplier

                # Store condition info for display
                self.condition_factor = condition_factor
                self.performance_multiplier = performance_multiplier

            except Exception as e:
                print(f"Error updating car performance with condition: {e}")
                # Fallback to base performance
                self.horsepower = self.base_horsepower
                self.max_speed = self.base_max_speed
                self.acceleration = self.base_acceleration
                self.condition_factor = 1.0
                self.performance_multiplier = 1.0
        else:
            # No car index (opponent cars), use base performance
            self.horsepower = self.base_horsepower
            self.max_speed = self.base_max_speed
            self.acceleration = self.base_acceleration
            self.condition_factor = 1.0
            self.performance_multiplier = 1.0

    def elapsed_time(self, map_distance):
        if self._pos.x >= map_distance:
            return time.time() - self.start_time

    def refresh_performance(self):
        """Refresh performance based on current condition - call this periodically during races"""
        self.update_performance_with_condition()

class Car(BaseCar):
    def __init__(self, name, color, weight, parts, x_cord, y_cord, start_time, car_index=None):
        super().__init__(name, color, weight, parts, x_cord, y_cord, start_time, car_index)
        self.performance_update_counter = 0

    def update(self, keys, dt):
        if keys is None or dt is None:
            return

        # Periodically update performance based on condition (every 5 seconds)
        self.performance_update_counter += dt
        if self.performance_update_counter >= 5.0:
            self.refresh_performance()
            self.performance_update_counter = 0

        if keys[pygame.K_w]:
            self.speed = min(self.speed + self.acceleration * dt, self.max_speed)
        else:
            self.speed = max(self.speed - self.acceleration * dt * 0.5, 0)
        self._pos.x += self.speed * dt
        self.x_cord = int(self._pos.x)

    def draw(self, screen):
        font = load_font("arial", 36)
        info_font = load_font("arial", 24)

        # Get screen dimensions for responsive positioning
        s_width, s_height = screen.get_size()

        # Main speed display - positioned responsively
        text = font.render(f"Twoja prędkość {int(self.speed // 5)} km/h", True, (255, 255, 255))
        # Create background for better visibility
        text_bg = pygame.Rect(20, 20, text.get_width() + 20, text.get_height() + 10)
        pygame.draw.rect(screen, (0, 0, 0, 180), text_bg)
        pygame.draw.rect(screen, (255, 255, 255), text_bg, 2)
        screen.blit(text, (30, 25))

        # Condition indicator - positioned below speed with proper spacing
        if hasattr(self, 'condition_factor'):
            condition_percent = int(self.condition_factor * 100)
            performance_percent = int(self.performance_multiplier * 100)

            # Color code based on condition
            if condition_percent >= 80:
                color = (0, 255, 0)  # Green
            elif condition_percent >= 60:
                color = (255, 255, 0)  # Yellow
            elif condition_percent >= 40:
                color = (255, 165, 0)  # Orange
            else:
                color = (255, 0, 0)  # Red

            condition_text = info_font.render(f"Stan: {condition_percent}% | Wydajność: {performance_percent}%", True, color)
            # Position below speed text with proper spacing
            condition_y = text_bg.bottom + 10
            condition_bg = pygame.Rect(20, condition_y, condition_text.get_width() + 20, condition_text.get_height() + 10)
            pygame.draw.rect(screen, (0, 0, 0, 180), condition_bg)
            pygame.draw.rect(screen, color, condition_bg, 2)
            screen.blit(condition_text, (30, condition_y + 5))

        screen.blit(self.img, (self.x_cord, self.y_cord))

    def set_static_position(self, pos_x):
        self._pos.x = pos_x
        self.x_cord = pos_x

class OpponentCar(BaseCar):
    def __init__(self, name, color, weight, parts, x_cord, y_cord, start_time, opponent_name="Przeciwnik"):
        super().__init__(name, color, weight, parts, x_cord, y_cord, start_time)
        self.opponent_name = opponent_name
        self._target_speed = self.max_speed * 0.95  # Target speed when race starts
        self.frame_count = 0
        self.race_started = False  # Track if race has started

    def update(self, dt):
        # Only update if race has started (dt > 0)
        if dt > 0:
            self.race_started = True

        # Don't update speed during countdown
        if not self.race_started or dt <= 0:
            self.speed = 0
            return

        # Now that race has started, update speed normally
        self.frame_count += 1
        if self.frame_count % 60 == 0:
            # Use a more stable target speed calculation
            variation = (self.frame_count // 60) % 10 * 0.01  # Small variation 0-0.09
            self._target_speed = self.max_speed * (0.9 + variation)

        # Accelerate towards target speed
        speed_diff = self._target_speed - self.speed
        self.speed += speed_diff * 0.05
        self.speed = min(self.speed, self.max_speed)
        self._pos.x += self.speed * dt
        self.x_cord = int(self._pos.x)

    def draw(self, screen, distance_covered, is_map_ended, bg_x_cord):
        x = self.x_cord - distance_covered if not is_map_ended else self.x_cord
        screen.blit(self.img, (x, self.y_cord))

        # Draw opponent info - positioned to avoid overlap with player info
        font = load_font("arial", 24)
        speed_text = font.render(f"{self.opponent_name}: {int(self.speed // 5)} km/h", True, (255, 255, 255))

        # Position opponent info below player info with proper spacing
        # Player info takes up approximately 100px height, so start at 120px
        opponent_y = 120
        opponent_bg = pygame.Rect(20, opponent_y, speed_text.get_width() + 20, speed_text.get_height() + 10)
        pygame.draw.rect(screen, (0, 0, 0, 180), opponent_bg)
        pygame.draw.rect(screen, (255, 255, 255), opponent_bg, 2)
        screen.blit(speed_text, (30, opponent_y + 5))