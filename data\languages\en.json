{"menu": {"main_menu": "Main Menu", "game": "Game", "cars": "Cars", "trade": "Trade", "settings": "Settings", "quit_game": "Quit Game", "back": "Back", "game_menu": "Game Menu", "settings_menu": "<PERSON><PERSON><PERSON>", "game_settings": "Game Settings"}, "game": {"single_player": "Single Player", "load_game": "Load Game", "save_game": "Save Game", "new_game": "New Game", "profile": "Profile"}, "profile": {"player": "Player", "player_level": "Player Level", "race_level": "Race Level", "money": "Money", "experience": "EXP", "vehicle_info": "Vehicle Information", "model": "Model", "weight": "Weight", "value": "Value", "engine_power": "Engine Power", "no_vehicle_selected": "No Vehicle Selected", "change_username": "Change Username"}, "settings": {"language": "Language", "language_selection": "Language Selection", "resolution": "Resolution", "fullscreen_mode": "Game always runs in fullscreen mode", "current_resolution": "Resolution: {width}x{height} (Fullscreen)"}, "languages": {"pl": "<PERSON><PERSON>", "en": "English", "de": "De<PERSON>ch", "es": "Español"}, "garage": {"garage": "Garage", "tuning": "Tuning", "weight": "Weight", "value": "Value", "power": "Power", "fuel": "Fuel", "condition": "Condition", "maintenance": "Maintenance", "repair": "Repair", "car_selection": "Car Selection", "tuning_garage": "Tuning (Garage)", "select_car": "Select Car", "install_part": "Install Part", "remove_part": "Remove Part", "part_installed": "Part Installed", "part_removed": "Part Removed", "no_compatible_parts": "No Compatible Parts", "part_compatibility": "Part Compatibility", "click_to_select": "Click on a car to select it. Use color buttons to change color.", "no_car_owned": "You don't own any cars!", "go_to_shop": "Go to the shop and buy a car", "engine_power": "Engine Power", "boost": "Boost", "power_to_weight_ratio": "Power to Weight Ratio", "power_change": "Power Change", "opponent_parts": "Opponent Parts", "repair_workshop": "Car Workshop", "car": "Car", "repair_options": "Repair Options", "maintenance_info": "Maintenance: Basic condition improvement (-5 days age)"}, "maintenance": {"minor_damage": "Minor damage", "moderate_damage": "Moderate damage", "major_damage": "Major damage", "total_damage": "Total destruction", "invalid_car_index": "Invalid car index", "insufficient_money": "You don't have enough money. You need", "maintenance_completed": "Maintenance completed for", "condition_improved": "Parts condition has been improved", "maintenance_error": "Error during maintenance", "full_restoration": "Full restoration", "major_repair": "Major repair", "minor_repair": "Minor repair", "invalid_repair_type": "Invalid repair type", "cannot_afford": "You can't afford", "cost": "Cost", "completed_for": "completed for", "repair_error": "Error during repair", "minor_repair_desc": "Basic repair - restores 15% condition", "major_repair_desc": "Significant repair - restores 40% condition", "full_restoration_desc": "Complete restoration - restores 70% condition", "invalid_insurance_plan": "Invalid insurance plan", "insurance_purchased": "Purchased", "for": "for", "insurance_error": "Error purchasing insurance"}, "messages": {"game_loaded": "Game has been loaded!", "game_saved": "Game has been saved!", "new_game_started": "New game started!", "language_changed": "Language has been changed!", "error_loading_profile": "Error loading profile!", "error_loading_garage": "Error loading garage data!", "error_loading_opponents": "Error loading opponent data!", "invalid_car_index": "Invalid car index!", "invalid_profile_data": "Invalid profile data!", "part_installed_successfully": "Part installed successfully!", "part_removed_successfully": "Part removed successfully!", "insufficient_money": "Insufficient money!", "car_purchased": "Car purchased!", "part_purchased": "Part purchased!", "item_sold": "Item sold!", "save_successful": "Save successful!", "load_successful": "Load successful!", "operation_cancelled": "Operation cancelled!", "confirm_action": "Confirm action?", "action_completed": "Action completed!", "error_occurred": "An error occurred!", "please_wait": "Please wait...", "loading": "Loading...", "saving": "Saving..."}, "shop": {"shop": "Shop", "cars": "Cars", "parts": "Parts", "buy": "Buy", "sell": "<PERSON>ll", "money": "Money", "owned": "Owned", "available": "Available", "purchase": "Purchase", "sale": "Sale", "price": "Price", "quantity": "Quantity", "total": "Total", "insufficient_funds": "Insufficient Funds", "purchase_successful": "Purchase Successful", "sale_successful": "Sale Successful", "item_purchased": "<PERSON><PERSON>chased", "item_sold": "<PERSON><PERSON>", "confirm_purchase": "Confirm Purchase", "confirm_sale": "Confirm Sale", "cancel_transaction": "Cancel Transaction"}, "race": {"level": "Level", "opponent": "Opponent", "time": "Time", "speed": "Speed", "distance": "Distance", "finish": "Finish", "winner": "Winner", "reward": "<PERSON><PERSON>", "experience": "Experience", "race_info": "Race Information", "start_race": "Start Race", "race_results": "Race Results", "you_won": "You Won!", "you_lost": "You Lost!", "race_time": "Race Time", "best_time": "Best Time", "position": "Position", "lap": "<PERSON><PERSON>", "countdown": "Countdown", "ready": "Ready", "go": "Go!", "your_speed": "Your Speed", "kmh": "km/h", "level_completed": "LEVEL {level} COMPLETED!", "level_up": "LEVEL UP TO {level}!", "your_time": "Your Time", "unknown_opponent": "Unknown", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "parts": {"engine": "Engine", "turbo": "Turbo", "intercooler": "Intercooler", "ecu": "ECU", "tires": "Tires", "horsepower": "Horsepower", "horsepower_boost": "Power Boost", "compatibility": "Compatibility", "part_name": "Part name", "part_type": "Part Type", "part_category": "Part Category", "installation": "Installation", "removal": "Removal", "upgrade": "Upgrade", "downgrade": "Downgrade", "performance": "Performance", "durability": "Durability", "cost": "Cost", "benefit": "Benefit", "category": "Category", "unknown_part": "Unknown part", "none": "None", "check_compatibility": "Check compatibility"}, "ui": {"confirm": "Confirm", "cancel": "Cancel", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "save": "Save", "load": "Load", "delete": "Delete", "edit": "Edit", "select": "Select", "next": "Next", "previous": "Previous", "continue": "Continue", "start": "Start", "stop": "Stop", "pause": "Pause", "resume": "Resume", "apply": "Apply", "reset": "Reset", "default": "<PERSON><PERSON><PERSON>", "custom": "Custom", "auto": "Auto", "manual": "Manual", "enabled": "Enabled", "disabled": "Disabled", "on": "On", "off": "Off", "high": "High", "medium": "Medium", "low": "Low", "none": "None", "all": "All", "refresh": "Refresh", "update": "Update", "upgrade": "Upgrade", "install": "Install", "uninstall": "Uninstall", "repair": "Repair", "replace": "Replace", "empty_slot": "Empty slot", "confirm_new_game": "Start new game?", "confirm_load_slot": "Load game from slot", "confirm_save_slot": "Save game to slot", "confirm_overwrite_slot": "Overwrite save in slot", "confirm_delete_slot": "Delete save from slot", "load_instructions": "Click on slot to load game. Right-click to delete save.", "save_instructions": "Click on slot to save game.", "press_enter_space_to_start": "Press ENTER or SPACE to start race", "auto_continue": "Auto continue in", "click_or_press_key_to_continue": "Click or press any key to continue", "enter_new_name_instruction": "Enter new name and press Enter or Save", "level_up": "LEVEL UP", "money_bonus": "Money bonus", "unlocked_parts": "Unlocked parts", "achievements": "Achievements", "special_bonuses": "Special bonuses"}, "selling": {"vehicle_sale": "Vehicle Sale", "model": "Model", "current_value": "Current value", "performance_bonus": "Performance bonus", "selling_price": "Selling price", "market_value": "market value", "parts_sold_with_car": "Parts sold with car", "total_parts_value": "Total parts value", "warning_parts_sold": "WARNING: These parts will be sold with the car!", "no_installed_parts": "No installed parts", "warning_last_car": "WARNING: This is your last car!", "wait_before_click": "Wait {time:.1f}s before clicking", "invalid_car_index": "Invalid car index", "sold": "Sold", "for": "for", "error_during_sale": "Error during sale", "sale": "Sale", "no_cars_in_garage": "No cars in garage", "click_sell_instruction": "Click 'Sell' to sell car", "warning_last_car_sale": "Warning: Selling last car - you'll need to buy a new one", "no_cars_owned": "You don't own any cars"}, "fuel": {"insufficient_money": "You don't have enough money. You need", "refueled": "Refueled", "for": "for", "refuel_error": "Error during refueling"}, "tires": {"racing_tires": "Racing Tires", "invalid_tire_type": "Invalid tire type", "insufficient_money": "You don't have enough money. You need", "tires_replaced": "Replaced tires with", "for": "for", "tire_replacement_error": "Error during tire replacement", "condition_excellent": "Excellent", "condition_good": "Good", "condition_average": "Average", "condition_poor": "Poor", "condition_critical": "Critical"}, "save": {"player": "Player", "level": "Level", "money": "Money", "saved_on": "Saved On", "playtime": "Playtime", "slot": "Slot"}, "units": {"kg": "kg", "hp": "HP", "currency": "$", "seconds": "s", "minutes": "min", "hours": "h", "days": "days", "level_short": "lvl", "experience_short": "exp", "liters": "L", "km": "km", "kmh": "km/h"}}