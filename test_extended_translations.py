"""
Test script for extended translations
"""

from language_manager import get_language_manager, get_text, set_language

def test_extended_translations():
    """Test the extended translations for all languages"""
    print("🧪 Testing Extended Translations...")
    
    lang_manager = get_language_manager()
    languages = ["pl", "en", "de", "es"]
    
    # Test categories and their keys
    test_keys = {
        "garage": [
            "car_selection", "tuning_garage", "select_car", "install_part", 
            "remove_part", "part_installed", "part_removed", "no_compatible_parts"
        ],
        "shop": [
            "purchase", "sale", "price", "quantity", "total", "insufficient_funds",
            "purchase_successful", "sale_successful", "confirm_purchase", "confirm_sale"
        ],
        "race": [
            "race_info", "start_race", "race_results", "you_won", "you_lost",
            "race_time", "best_time", "position", "countdown", "ready", "go"
        ],
        "parts": [
            "part_name", "part_type", "part_category", "installation", "removal",
            "upgrade", "downgrade", "performance", "durability", "cost", "benefit"
        ],
        "messages": [
            "part_installed_successfully", "part_removed_successfully", "insufficient_money",
            "car_purchased", "part_purchased", "item_sold", "save_successful",
            "load_successful", "operation_cancelled", "confirm_action", "action_completed"
        ],
        "ui": [
            "apply", "reset", "default", "custom", "auto", "manual", "enabled",
            "disabled", "on", "off", "high", "medium", "low", "refresh", "install"
        ]
    }
    
    for lang in languages:
        print(f"\n🌍 Testing {lang.upper()} translations...")
        set_language(lang)
        
        for category, keys in test_keys.items():
            print(f"  📂 {category}:")
            for key in keys:
                full_key = f"{category}.{key}"
                translation = get_text(full_key)
                
                # Check if translation is missing or is a fallback
                if translation.startswith("[MISSING:") or translation.startswith("[ERROR:"):
                    print(f"    ❌ {key}: {translation}")
                else:
                    print(f"    ✅ {key}: {translation}")
    
    print("\n🎉 Extended translations test completed!")

def test_specific_ui_elements():
    """Test specific UI elements that are commonly used"""
    print("\n🧪 Testing Specific UI Elements...")
    
    # Test common UI combinations
    ui_scenarios = [
        ("garage.car_selection", "Car selection screen"),
        ("garage.tuning_garage", "Tuning garage screen"),
        ("shop.confirm_purchase", "Purchase confirmation"),
        ("race.start_race", "Race start button"),
        ("messages.language_changed", "Language change confirmation"),
        ("ui.confirm", "Confirmation button"),
        ("ui.cancel", "Cancel button")
    ]
    
    languages = ["pl", "en", "de", "es"]
    
    for lang in languages:
        print(f"\n🌍 {lang.upper()} UI Elements:")
        set_language(lang)
        
        for key, description in ui_scenarios:
            translation = get_text(key)
            print(f"  {description}: {translation}")

def test_message_formatting():
    """Test message formatting with parameters"""
    print("\n🧪 Testing Message Formatting...")
    
    languages = ["pl", "en", "de", "es"]
    
    for lang in languages:
        print(f"\n🌍 {lang.upper()} Formatted Messages:")
        set_language(lang)
        
        # Test resolution formatting
        resolution_text = get_text("settings.current_resolution", width=1920, height=1080)
        print(f"  Resolution: {resolution_text}")
        
        # Test other potential formatted messages
        # (These might not exist yet, but we can test the fallback)
        test_formats = [
            ("messages.money_earned", {"amount": 500}),
            ("race.lap_time", {"time": "1:23.45"}),
            ("parts.horsepower_info", {"hp": 350})
        ]
        
        for key, params in test_formats:
            try:
                formatted_text = get_text(key, **params)
                print(f"  {key}: {formatted_text}")
            except Exception as e:
                print(f"  {key}: [Format test - {e}]")

if __name__ == "__main__":
    print("🚀 Starting Extended Translations Tests")
    print("=" * 60)
    
    test_extended_translations()
    test_specific_ui_elements()
    test_message_formatting()
    
    print("\n" + "=" * 60)
    print("✨ All extended translation tests completed!")
