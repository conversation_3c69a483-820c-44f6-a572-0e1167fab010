import pygame
from utils import load_image

class Background:
    def __init__(self, name, screen_width, screen_height):
        self.x_cord = 0
        self.y_cord = 0
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.original_img = load_image(f'./assets/img/{name}.png')

        # Store original dimensions for proper scaling calculations
        self.original_width = self.original_img.get_width()
        self.original_height = self.original_img.get_height()

        # Scale the background to fit the screen properly
        self._scale_background()

    def _scale_background(self):
        """Scale the background image to fit the screen while maintaining aspect ratio or stretching to fill"""
        # Option 1: Stretch to fill entire screen (may distort aspect ratio)
        self.width = self.screen_width
        self.height = self.screen_height

        # Scale the image to the new dimensions
        self.img = pygame.transform.scale(self.original_img, (self.width, self.height))

    def update_resolution(self, screen_width, screen_height):
        """Update background when resolution changes"""
        self.screen_width = screen_width
        self.screen_height = screen_height
        self._scale_background()

    def draw(self, screen):
        screen.blit(self.img, (self.x_cord, self.y_cord))