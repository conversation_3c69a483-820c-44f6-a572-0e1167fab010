"""
Integration test for language system in the game
"""

import pygame
import json
from game import Game
from language_manager import get_language_manager
from utils import resource_path

def test_game_language_integration():
    """Test language integration with the actual game"""
    print("🧪 Testing Game Language Integration...")
    
    # Initialize pygame (required for Game class)
    pygame.init()
    pygame.font.init()
    
    try:
        # Test 1: Game initialization with language system
        print("\n1. Testing game initialization with language system...")
        game = Game()
        print("✅ Game initialized successfully with language system")
        print(f"   Current language: {game.language_manager.get_current_language()}")
        
        # Test 2: Test profile language loading
        print("\n2. Testing profile language loading...")
        try:
            with open(resource_path('data/profile.json'), 'r', encoding='utf-8') as f:
                profile_data = json.load(f)
            
            # Check if language setting exists
            current_lang = profile_data.get('settings', {}).get('language', 'pl')
            print(f"✅ Profile language setting: {current_lang}")
            
            # Test language loading from profile
            game.load_language_preferences()
            print(f"✅ Language loaded from profile: {game.language_manager.get_current_language()}")
            
        except Exception as e:
            print(f"⚠️  Profile language test: {e}")
        
        # Test 3: Test language switching and saving
        print("\n3. Testing language switching and saving...")
        original_lang = game.language_manager.get_current_language()
        
        # Switch to English
        success = game.language_manager.set_language("en")
        if success:
            print("✅ Successfully switched to English")
            
            # Save preferences
            game.save_language_preferences()
            print("✅ Language preferences saved")
            
            # Verify it was saved
            with open(resource_path('data/profile.json'), 'r', encoding='utf-8') as f:
                updated_profile = json.load(f)
            saved_lang = updated_profile.get('settings', {}).get('language', 'not found')
            print(f"✅ Verified saved language: {saved_lang}")
            
            # Switch back to original
            game.language_manager.set_language(original_lang)
            game.save_language_preferences()
            print(f"✅ Restored original language: {original_lang}")
        
        # Test 4: Test UI text generation
        print("\n4. Testing UI text generation...")
        test_languages = ["pl", "en", "de", "es"]
        
        for lang in test_languages:
            game.language_manager.set_language(lang)
            main_menu = game.language_manager.get_text("menu.main_menu")
            game_text = game.language_manager.get_text("menu.game")
            settings_text = game.language_manager.get_text("menu.settings")
            print(f"✅ {lang}: {main_menu} | {game_text} | {settings_text}")
        
        # Test 5: Test message formatting
        print("\n5. Testing message formatting...")
        game.language_manager.set_language("en")
        resolution_msg = game.language_manager.get_text("settings.current_resolution", width=1920, height=1080)
        print(f"✅ Formatted message: {resolution_msg}")
        
        # Test 6: Test dropdown options
        print("\n6. Testing dropdown options...")
        available_langs = game.language_manager.get_available_languages()
        print(f"✅ Available languages for dropdown: {available_langs}")
        
        print("\n🎉 Game language integration tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Game language integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        pygame.quit()

def test_ui_components():
    """Test UI components with language system"""
    print("\n🧪 Testing UI Components with Language System...")
    
    try:
        from ui_components import DropdownButton
        from language_manager import get_language_manager
        
        # Initialize pygame for UI components
        pygame.init()
        pygame.font.init()
        
        lang_manager = get_language_manager()
        
        # Test dropdown creation
        print("\n1. Testing DropdownButton creation...")
        dropdown = DropdownButton(
            100, 100, 200, 40,
            lang_manager.get_available_languages(),
            lang_manager.get_current_language(),
            24,
            lambda x: print(f"Selected: {x}")
        )
        print("✅ DropdownButton created successfully")
        print(f"   Options: {dropdown.options}")
        print(f"   Selected: {dropdown.get_selected_option()}")
        
        # Test option setting
        print("\n2. Testing option setting...")
        dropdown.set_selected_option("en")
        print(f"✅ Option set to: {dropdown.get_selected_option()}")
        
        print("\n🎉 UI components tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ UI components test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        pygame.quit()

if __name__ == "__main__":
    print("🚀 Starting Language Integration Tests")
    print("=" * 60)
    
    success1 = test_game_language_integration()
    success2 = test_ui_components()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("✨ All integration tests passed!")
    else:
        print("❌ Some integration tests failed!")
    
    print("🏁 Integration tests completed!")
