
import pygame
from utils import load_font, load_image
from cursor_manager import cursor_manager

class CarCard:
    def __init__(self, car_data, x, y, width, height, is_selected=False):
        self.car_data = car_data
        self.rect = pygame.Rect(x, y, width, height)
        self.is_selected = is_selected
        self.is_hovered = False
        self.selected_color_index = "0"  # Default color
        self.font = load_font("arial", 24)
        self.header_font = load_font("arial", 32)
        
        # Load car image
        self.car_image = None
        self.load_car_image()
        
    def load_car_image(self):
        try:
            # Load car image without color system
            image_path = f"assets/img/cars/{self.car_data['name']}_car.png"
            self.car_image = load_image(image_path)
            # Scale to appropriate size
            self.car_image = pygame.transform.scale(self.car_image, (180, 120))
        except Exception as e:
            print(f"Error loading car image: {e}")
            # Create a placeholder rectangle if image not found
            self.car_image = pygame.Surface((150, 100))
            self.car_image.fill((100, 100, 100))
    
    def update(self, mouse_pos, mouse_click=None):
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        # Legacy support for mouse_click parameter (deprecated)
        if mouse_click is not None and self.is_hovered and mouse_click[0]:
            return True  # Card was clicked
        return False

    def handle_event(self, event, mouse_pos):
        """Handle mouse events - preferred method"""
        self.is_hovered = self.rect.collidepoint(mouse_pos)

        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:  # Left click
            if self.is_hovered:
                return True  # Card was clicked
        return False
    

    
    def draw(self, screen):
        # Draw card background
        bg_color = (80, 80, 80) if self.is_selected else (50, 50, 50)
        if self.is_hovered:
            bg_color = (100, 100, 100)
        
        pygame.draw.rect(screen, bg_color, self.rect)
        
        # Draw border
        border_color = (0, 255, 255) if self.is_selected else (200, 200, 200)
        border_width = 3 if self.is_selected else 1
        pygame.draw.rect(screen, border_color, self.rect, border_width)
        
        # Draw car image
        if self.car_image:
            image_x = self.rect.x + (self.rect.width - self.car_image.get_width()) // 2
            image_y = self.rect.y + 10
            screen.blit(self.car_image, (image_x, image_y))
        
        # Draw car info
        y_offset = self.rect.y + 120
        
        # Car name
        name_text = self.header_font.render(self.car_data["name"].title(), True, (255, 255, 255))
        screen.blit(name_text, (self.rect.x + 10, y_offset))
        y_offset += 35
        
        # Car stats - removed color display
        
        weight_text = self.font.render(f"Waga: {self.car_data['weight']} kg", True, (200, 200, 200))
        screen.blit(weight_text, (self.rect.x + 10, y_offset))
        y_offset += 25
        
        value_text = self.font.render(f"Wartość: {self.car_data['value']} $", True, (200, 200, 200))
        screen.blit(value_text, (self.rect.x + 10, y_offset))
        y_offset += 25
        
        # Calculate total horsepower: engine base + sum of horsepower_boost from mounted parts
        engine_part = self.car_data['parts'].get('engine')
        total_horsepower = engine_part.get('horsepower', 0) if engine_part else 0
        for part_key, part in self.car_data['parts'].items():
            if part_key != 'engine' and part is not None:
                horsepower_boost = part.get('horsepower_boost', 0)
                total_horsepower += horsepower_boost
        
        hp_text = self.font.render(f"Moc: {total_horsepower} KM", True, (200, 200, 200))
        screen.blit(hp_text, (self.rect.x + 10, y_offset))

class TextButton:
    def __init__(self, label, x, y, font_name='arial', font_size=48, action=None, width=None, height=None):
        self.label = label
        self.x = x
        self.y = y
        self.font_name = font_name
        self.font_size = font_size  # Store font_size as attribute
        self.font = load_font(font_name, font_size)
        self.default_color = (200, 200, 200)
        self.hover_color = (0, 255, 255)
        self.action = action
        self.is_hovered = False
        self.width = width
        self.height = height
        self.was_clicked = False  # Track if button was clicked this frame
        self.last_click_time = 0  # Track last click time for debouncing
        self.click_cooldown = 200  # Minimum time between clicks in milliseconds
        self.render()

    def render(self):
        color = self.hover_color if self.is_hovered else self.default_color
        self.surface = self.font.render(self.label, True, color)



        # If width and height are specified, create a button background
        if self.width and self.height:
            # Create a surface for the button background
            button_surface = pygame.Surface((self.width, self.height))
            button_surface.fill((50, 50, 50))  # Dark gray background

            # Add border
            border_color = self.hover_color if self.is_hovered else (100, 100, 100)
            pygame.draw.rect(button_surface, border_color, (0, 0, self.width, self.height), 2)

            # Center the text on the button
            text_rect = self.surface.get_rect(center=(self.width // 2, self.height // 2))
            button_surface.blit(self.surface, text_rect)

            self.surface = button_surface
            self.rect = pygame.Rect(self.x, self.y, self.width, self.height)


        else:
            # Original behavior for backward compatibility
            self.rect = self.surface.get_rect(topleft=(self.x, self.y))

    def update(self, mouse_pos, mouse_click=None):
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        self.render()
        self.was_clicked = False  # Reset click state

        # Register hover state with cursor manager
        cursor_manager.register_hoverable(self.is_hovered)

        # Legacy support for mouse_click parameter (deprecated)
        if mouse_click is not None and self.is_hovered and mouse_click[0] and self.action:
            self.action()

    def handle_event(self, event, mouse_pos):
        """Handle mouse events - preferred method"""
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        self.render()

        # Register hover state with cursor manager
        cursor_manager.register_hoverable(self.is_hovered)

        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:  # Left click
            if self.is_hovered:
                current_time = pygame.time.get_ticks()
                # Check if enough time has passed since last click (debouncing)
                if current_time - self.last_click_time >= self.click_cooldown:
                    self.was_clicked = True
                    self.last_click_time = current_time
                    if self.action:
                        self.action()
                    return True  # Button was clicked
                else:
                    return False  # Click was debounced
        return False

    def draw(self, screen):
        screen.blit(self.surface, self.rect)
