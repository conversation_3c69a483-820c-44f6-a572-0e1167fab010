#!/usr/bin/env python3
"""
Test script for language detection system
"""

import locale
from language_manager import detect_system_language, get_language_manager

def test_language_detection():
    """Test the language detection system"""
    print("🌍 Language Detection Test")
    print("=" * 50)
    
    # Test system language detection
    try:
        system_locale = locale.getdefaultlocale()
        print(f"System locale: {system_locale}")
        
        detected_lang = detect_system_language()
        print(f"Detected language: {detected_lang}")
        
        # Test language manager initialization
        lm = get_language_manager()
        print(f"Language manager default: {lm.get_current_language()}")
        print(f"Language name: {lm.get_current_language_name()}")
        
        # Test text retrieval
        test_text = lm.get_text("menu.main_menu")
        print(f"Test text (menu.main_menu): {test_text}")
        
        # Test fallback system
        print("\n🔄 Testing fallback system...")
        
        # Test with all languages
        for lang_code, lang_name in lm.get_available_languages().items():
            lm.set_language(lang_code)
            text = lm.get_text("menu.main_menu")
            print(f"  {lang_code} ({lang_name}): {text}")
        
        # Test missing key fallback
        print("\n🚫 Testing missing key fallback...")
        lm.set_language("de")  # German might have missing keys
        missing_text = lm.get_text("nonexistent.key")
        print(f"Missing key result: {missing_text}")
        
        print("\n✅ Language detection test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during language detection test: {e}")
        return False

def test_different_locales():
    """Test how the system would behave with different locales"""
    print("\n🌐 Simulating different system locales...")
    
    test_locales = [
        ("en_US", "en"),
        ("en_GB", "en"), 
        ("pl_PL", "pl"),
        ("de_DE", "de"),
        ("es_ES", "es"),
        ("fr_FR", "en"),  # French should fallback to English
        ("ja_JP", "en"),  # Japanese should fallback to English
        ("zh_CN", "en"),  # Chinese should fallback to English
        (None, "en"),     # None should fallback to English
    ]
    
    for test_locale, expected_lang in test_locales:
        # Simulate what detect_system_language would return
        if test_locale:
            lang_code = test_locale[:2].lower()
            supported_languages = {"pl", "en", "de", "es"}
            result = lang_code if lang_code in supported_languages else "en"
        else:
            result = "en"
        
        status = "✅" if result == expected_lang else "❌"
        print(f"  {status} {test_locale or 'None'} -> {result} (expected: {expected_lang})")

if __name__ == "__main__":
    test_language_detection()
    test_different_locales()
