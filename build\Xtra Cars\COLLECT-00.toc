([('Xtra Cars.exe',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\build\\Xtra Cars\\Xtra Cars.exe',
   'EXECUTABLE'),
  ('python313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll',
   'BINARY'),
  ('freetype.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\freetype.dll',
   'BINARY'),
  ('SDL2_ttf.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\SDL2_ttf.dll',
   'BINARY'),
  ('libpng16-16.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\libpng16-16.dll',
   'BINARY'),
  ('portmidi.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\portmidi.dll',
   'BINARY'),
  ('libopus-0.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\libopus-0.dll',
   'BINARY'),
  ('libwebp-7.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\libwebp-7.dll',
   'BINARY'),
  ('libtiff-5.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\libtiff-5.dll',
   'BINARY'),
  ('SDL2_image.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\SDL2_image.dll',
   'BINARY'),
  ('SDL2.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\SDL2.dll',
   'BINARY'),
  ('SDL2_mixer.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\SDL2_mixer.dll',
   'BINARY'),
  ('libjpeg-9.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\libjpeg-9.dll',
   'BINARY'),
  ('libogg-0.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\libogg-0.dll',
   'BINARY'),
  ('libmodplug-1.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\libmodplug-1.dll',
   'BINARY'),
  ('libopusfile-0.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\libopusfile-0.dll',
   'BINARY'),
  ('zlib1.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\zlib1.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('pygame\\imageext.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\imageext.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\scrap.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\scrap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\mixer.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\mixer.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\mixer_music.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\mixer_music.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\font.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\font.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_freetype.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\_freetype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\transform.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\transform.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\time.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\time.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\pixelarray.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\pixelarray.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\mask.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\mask.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\surface.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\surface.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\pixelcopy.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\pixelcopy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\mouse.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\mouse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\key.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\key.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\joystick.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\joystick.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\image.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\image.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\event.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\event.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\draw.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\draw.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\display.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\display.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\math.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\math.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\bufferproxy.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\bufferproxy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\color.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\color.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\surflock.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\surflock.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\rwobject.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\rwobject.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\rect.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\rect.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\constants.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\constants.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\base.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\base.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('pygame\\SDL2.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\SDL2.dll',
   'BINARY'),
  ('pygame\\zlib1.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\zlib1.dll',
   'BINARY'),
  ('pygame\\libjpeg-9.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\libjpeg-9.dll',
   'BINARY'),
  ('pygame\\libogg-0.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\libogg-0.dll',
   'BINARY'),
  ('pygame\\libopus-0.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\libopus-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('pygame\\libpng16-16.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\libpng16-16.dll',
   'BINARY'),
  ('pygame\\SDL2_image.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\SDL2_image.dll',
   'BINARY'),
  ('pygame\\SDL2_mixer.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\SDL2_mixer.dll',
   'BINARY'),
  ('pygame\\SDL2_ttf.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\SDL2_ttf.dll',
   'BINARY'),
  ('pygame\\freetype.dll',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\freetype.dll',
   'BINARY'),
  ('assets\\img\\background.png',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\assets\\img\\background.png',
   'DATA'),
  ('assets\\img\\cars\\Xtreme_car.png',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\assets\\img\\cars\\Xtreme_car.png',
   'DATA'),
  ('assets\\img\\cars\\future_car.png',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\assets\\img\\cars\\future_car.png',
   'DATA'),
  ('assets\\img\\cars\\old_car.png',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\assets\\img\\cars\\old_car.png',
   'DATA'),
  ('assets\\img\\icon.ico',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\assets\\img\\icon.ico',
   'DATA'),
  ('assets\\img\\icon.png',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\assets\\img\\icon.png',
   'DATA'),
  ('assets\\img\\maps\\map1.png',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\assets\\img\\maps\\map1.png',
   'DATA'),
  ('assets\\sounds\\overheating_car.wav',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\assets\\sounds\\overheating_car.wav',
   'DATA'),
  ('data\\garage.json',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\data\\garage.json',
   'DATA'),
  ('data\\languages\\de.json',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\data\\languages\\de.json',
   'DATA'),
  ('data\\languages\\en.json',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\data\\languages\\en.json',
   'DATA'),
  ('data\\languages\\es.json',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\data\\languages\\es.json',
   'DATA'),
  ('data\\languages\\pl.json',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\data\\languages\\pl.json',
   'DATA'),
  ('data\\oponent_levels.json',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\data\\oponent_levels.json',
   'DATA'),
  ('data\\profile.json',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\data\\profile.json',
   'DATA'),
  ('data\\shop_data.json',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\data\\shop_data.json',
   'DATA'),
  ('pygame\\freesansbold.ttf',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\freesansbold.ttf',
   'DATA'),
  ('pygame\\pygame_icon.bmp',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\pygame\\pygame_icon.bmp',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\Desktop\\Xtra '
   'Cars\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\Xtra Cars\\build\\Xtra Cars\\base_library.zip',
   'DATA')],)
