"""
Test script for resolution handling functionality.
This script tests various resolutions to ensure proper scaling and positioning.
"""

import pygame
import sys
import time
from background import Background
from map import Map
from resolution_manager import ResolutionManager
from ui_components import TextButton


def test_resolution_scaling():
    """Test resolution scaling with different screen sizes"""
    pygame.init()
    pygame.font.init()
    
    # Test resolutions
    test_resolutions = [
        (1024, 768, "1024x768"),
        (1280, 720, "1280x720 (HD)"),
        (1366, 768, "1366x768"),
        (1920, 1080, "1920x1080 (Full HD)"),
        (2560, 1440, "2560x1440 (QHD)")
    ]
    
    print("Testing resolution handling...")
    
    for width, height, name in test_resolutions:
        print(f"\nTesting resolution: {name}")
        
        try:
            # Try to set the resolution
            screen = pygame.display.set_mode((width, height))
            actual_width, actual_height = screen.get_size()
            
            print(f"  Requested: {width}x{height}")
            print(f"  Actual: {actual_width}x{actual_height}")
            
            # Test background scaling
            try:
                background = Background('background', actual_width, actual_height)
                print(f"  Background scaled to: {background.width}x{background.height}")
                
                # Verify background covers the screen
                if background.width == actual_width and background.height == actual_height:
                    print("  ✓ Background scaling: PASS")
                else:
                    print("  ✗ Background scaling: FAIL")
                    
            except Exception as e:
                print(f"  ✗ Background test failed: {e}")
            
            # Test map scaling
            try:
                test_map = Map('map1', actual_width, actual_height)
                print(f"  Map scaled to: {test_map.width}x{test_map.height}")
                
                # Verify map height matches screen height
                if test_map.height == actual_height:
                    print("  ✓ Map scaling: PASS")
                else:
                    print("  ✗ Map scaling: FAIL")
                    
            except Exception as e:
                print(f"  ✗ Map test failed: {e}")
            
            # Test resolution manager
            try:
                resolution_manager = ResolutionManager(actual_width, actual_height)
                
                # Test UI scaling
                scaled_font = resolution_manager.get_scaled_font_size(24)
                scaled_spacing = resolution_manager.get_scaled_spacing(20)
                scaled_button = resolution_manager.get_scaled_button_size(200, 50)
                
                print(f"  Scaled font size (24): {scaled_font}")
                print(f"  Scaled spacing (20): {scaled_spacing}")
                print(f"  Scaled button size (200x50): {scaled_button}")
                print("  ✓ Resolution manager: PASS")
                
            except Exception as e:
                print(f"  ✗ Resolution manager test failed: {e}")
            
            # Test safe area calculation
            try:
                safe_area = resolution_manager.get_safe_area(50)
                print(f"  Safe area: {safe_area}")
                
                # Verify safe area is within screen bounds
                if (safe_area[0] >= 0 and safe_area[1] >= 0 and 
                    safe_area[0] + safe_area[2] <= actual_width and 
                    safe_area[1] + safe_area[3] <= actual_height):
                    print("  ✓ Safe area calculation: PASS")
                else:
                    print("  ✗ Safe area calculation: FAIL")
                    
            except Exception as e:
                print(f"  ✗ Safe area test failed: {e}")
            
            # Brief pause to allow visual inspection if needed
            time.sleep(0.1)
            
        except Exception as e:
            print(f"  ✗ Failed to set resolution {name}: {e}")
    
    pygame.quit()
    print("\nResolution testing completed.")


def test_ui_positioning():
    """Test UI element positioning at different resolutions"""
    pygame.init()
    pygame.font.init()
    
    print("\nTesting UI positioning...")
    
    test_resolutions = [
        (1024, 768),
        (1920, 1080),
        (2560, 1440)
    ]
    
    for width, height in test_resolutions:
        print(f"\nTesting UI positioning at {width}x{height}")
        
        try:
            screen = pygame.display.set_mode((width, height))
            resolution_manager = ResolutionManager(width, height)
            
            # Test center positioning
            center_x, center_y = resolution_manager.get_center_position(200, 100)
            expected_x = (width - 200) // 2
            expected_y = (height - 100) // 2
            
            if center_x == expected_x and center_y == expected_y:
                print("  ✓ Center positioning: PASS")
            else:
                print(f"  ✗ Center positioning: FAIL (got {center_x},{center_y}, expected {expected_x},{expected_y})")
            
            # Test coordinate scaling
            scaled_x, scaled_y = resolution_manager.scale_coordinate(100, 100, 1920, 1080)
            expected_scale_x = int(100 * width / 1920)
            expected_scale_y = int(100 * height / 1080)
            
            if scaled_x == expected_scale_x and scaled_y == expected_scale_y:
                print("  ✓ Coordinate scaling: PASS")
            else:
                print(f"  ✗ Coordinate scaling: FAIL (got {scaled_x},{scaled_y}, expected {expected_scale_x},{expected_scale_y})")
            
            # Test clamping to screen
            clamped_x, clamped_y = resolution_manager.clamp_to_screen(width + 100, height + 100, 200, 100)
            max_x = width - 200 - 10  # 10 is default margin
            max_y = height - 100 - 10
            
            if clamped_x <= max_x and clamped_y <= max_y:
                print("  ✓ Screen clamping: PASS")
            else:
                print(f"  ✗ Screen clamping: FAIL (got {clamped_x},{clamped_y}, max should be {max_x},{max_y})")
                
        except Exception as e:
            print(f"  ✗ UI positioning test failed: {e}")
    
    pygame.quit()
    print("UI positioning testing completed.")


def test_fullscreen_transitions():
    """Test transitions between windowed and fullscreen modes"""
    pygame.init()
    pygame.font.init()
    
    print("\nTesting fullscreen transitions...")
    
    try:
        # Start in windowed mode
        screen = pygame.display.set_mode((1024, 768))
        windowed_size = screen.get_size()
        print(f"Windowed mode: {windowed_size}")
        
        # Switch to fullscreen
        screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
        fullscreen_size = screen.get_size()
        print(f"Fullscreen mode: {fullscreen_size}")
        
        # Switch back to windowed
        screen = pygame.display.set_mode((1280, 720))
        windowed_size2 = screen.get_size()
        print(f"Windowed mode 2: {windowed_size2}")
        
        print("✓ Fullscreen transitions: PASS")
        
    except Exception as e:
        print(f"✗ Fullscreen transitions: FAIL - {e}")
    
    pygame.quit()
    print("Fullscreen transition testing completed.")


if __name__ == "__main__":
    print("=== Resolution Handling Test Suite ===")
    
    try:
        test_resolution_scaling()
        test_ui_positioning()
        test_fullscreen_transitions()
        
        print("\n=== Test Summary ===")
        print("All resolution handling tests completed.")
        print("Check the output above for any FAIL messages.")
        print("If all tests show PASS, the resolution handling is working correctly.")
        
    except Exception as e:
        print(f"Test suite failed with error: {e}")
        sys.exit(1)
