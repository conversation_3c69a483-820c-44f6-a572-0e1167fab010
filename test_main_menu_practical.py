"""
Practical test script for main menu resolution compatibility.
This script launches the actual game and tests main menu functionality.
"""

import pygame
import sys
import time
import traceback
from game import Game


def test_resolution_manager_integration():
    """Test if resolution manager is properly integrated"""
    print("Testing resolution manager integration...")
    
    try:
        from resolution_manager import get_resolution_manager, init_resolution_manager
        print("  ✓ Resolution manager imports successful")
        
        # Test initialization
        init_resolution_manager(1920, 1080)
        rm = get_resolution_manager()
        print(f"  ✓ Resolution manager initialized: {rm.current_width}x{rm.current_height}")
        
        # Test scaling functions
        font_size = rm.get_scaled_font_size(36)
        button_size = rm.get_scaled_button_size(250, 60)
        spacing = rm.get_scaled_spacing(75)
        
        print(f"  ✓ Scaling functions work: font={font_size}, button={button_size}, spacing={spacing}")
        return True
        
    except Exception as e:
        print(f"  ✗ Resolution manager integration failed: {e}")
        traceback.print_exc()
        return False


def test_textbutton_parameters():
    """Test if TextButton properly handles width, height, font_size parameters"""
    print("\nTesting TextButton parameter handling...")
    
    try:
        pygame.init()
        pygame.font.init()
        screen = pygame.display.set_mode((1024, 768))
        
        from ui_components import TextButton
        
        # Test button with explicit parameters
        button = TextButton("Test Button", 100, 100, 
                          font_size=24, width=200, height=50)
        
        print(f"  ✓ TextButton created with parameters")
        print(f"    Position: ({button.x}, {button.y})")
        print(f"    Font size: {button.font_size}")
        
        if hasattr(button, 'width') and hasattr(button, 'height'):
            print(f"    Size: {button.width}x{button.height}")
        else:
            print("    ⚠ Button doesn't store width/height attributes")
        
        # Test button rendering
        button.draw(screen)
        print("  ✓ Button renders without errors")
        
        pygame.quit()
        return True
        
    except Exception as e:
        print(f"  ✗ TextButton test failed: {e}")
        traceback.print_exc()
        pygame.quit()
        return False


def test_main_menu_at_resolution(width, height, resolution_name):
    """Test main menu at a specific resolution"""
    print(f"\nTesting main menu at {resolution_name}...")
    
    try:
        pygame.init()
        pygame.font.init()
        
        # Create game instance
        game = Game()
        
        # Change to test resolution
        if width == "fullscreen":
            game.change_resolution("fullscreen", "fullscreen")
        else:
            game.change_resolution(width, height)
        
        actual_width, actual_height = game.s_width, game.s_height
        print(f"  Actual screen size: {actual_width}x{actual_height}")
        print(f"  Fullscreen mode: {game.is_fullscreen}")
        
        # Test resolution manager state
        try:
            from resolution_manager import get_resolution_manager
            rm = get_resolution_manager()
            print(f"  Resolution manager state: {rm.current_width}x{rm.current_height}")
            
            # Get scaling values that main menu should use
            button_font_size = rm.get_scaled_font_size(36)
            button_width, button_height = rm.get_scaled_button_size(250, 60)
            button_spacing = rm.get_scaled_spacing(75)
            title_font_size = rm.get_scaled_font_size(48)
            title_space = rm.get_scaled_spacing(120)
            
            print(f"  Expected scaling values:")
            print(f"    Button font: {button_font_size}")
            print(f"    Button size: {button_width}x{button_height}")
            print(f"    Button spacing: {button_spacing}")
            print(f"    Title font: {title_font_size}")
            print(f"    Title space: {title_space}")
            
        except Exception as e:
            print(f"  ✗ Resolution manager access failed: {e}")
            return False
        
        # Test main menu button creation logic
        try:
            button_labels = ['Gra', 'Samochody', 'Handel', 'Ustawienia', 'Wyjdź z gry']
            total_buttons_height = len(button_labels) * button_height + (len(button_labels) - 1) * button_spacing
            available_height = actual_height - title_space
            start_y = title_space + (available_height - total_buttons_height) // 2
            
            print(f"  Button positioning calculations:")
            print(f"    Total buttons height: {total_buttons_height}")
            print(f"    Available height: {available_height}")
            print(f"    Start Y: {start_y}")
            
            # Check if buttons fit
            last_button_y = start_y + (len(button_labels) - 1) * (button_height + button_spacing) + button_height
            if last_button_y <= actual_height:
                print(f"    ✓ Buttons fit within screen (last button at {last_button_y})")
            else:
                print(f"    ✗ Buttons exceed screen (last button at {last_button_y}, screen height {actual_height})")
                return False
            
            # Check button centering
            button_x = (actual_width - button_width) // 2
            if button_x >= 0 and button_x + button_width <= actual_width:
                print(f"    ✓ Buttons properly centered (x={button_x})")
            else:
                print(f"    ✗ Buttons not centered properly (x={button_x}, width={button_width})")
                return False
                
        except Exception as e:
            print(f"  ✗ Button positioning test failed: {e}")
            return False
        
        pygame.quit()
        print(f"  ✓ {resolution_name} test: PASS")
        return True
        
    except Exception as e:
        print(f"  ✗ {resolution_name} test failed: {e}")
        traceback.print_exc()
        pygame.quit()
        return False


def test_actual_main_menu_rendering():
    """Test actual main menu rendering by calling the main_menu method"""
    print("\nTesting actual main menu rendering...")
    
    test_resolutions = [
        (1024, 768, "1024x768"),
        (1920, 1080, "1920x1080"),
        ("fullscreen", "fullscreen", "Fullscreen")
    ]
    
    for width, height, name in test_resolutions:
        print(f"\n  Testing main menu rendering at {name}...")
        
        try:
            pygame.init()
            pygame.font.init()
            
            # Create game and set resolution
            game = Game()
            if width == "fullscreen":
                game.change_resolution("fullscreen", "fullscreen")
            else:
                game.change_resolution(width, height)
            
            print(f"    Screen size: {game.s_width}x{game.s_height}")
            
            # Try to create main menu components (without running the loop)
            try:
                from resolution_manager import get_resolution_manager
                resolution_manager = get_resolution_manager()
                
                # Get responsive scaling values (same as in main_menu method)
                button_font_size = resolution_manager.get_scaled_font_size(36)
                button_width, button_height = resolution_manager.get_scaled_button_size(250, 60)
                button_spacing = resolution_manager.get_scaled_spacing(75)
                title_font_size = resolution_manager.get_scaled_font_size(48)
                
                print(f"    ✓ Resolution manager scaling successful")
                
            except ImportError:
                # Fallback values if resolution manager not available
                button_font_size = 36
                button_width, button_height = 250, 60
                button_spacing = 75
                title_font_size = 48
                print(f"    ⚠ Using fallback values (resolution manager not available)")
            
            # Test button creation
            from ui_components import TextButton
            button_labels = ['Gra', 'Samochody', 'Handel', 'Ustawienia', 'Wyjdź z gry']
            button_actions = [lambda: None] * 5  # Dummy actions
            
            # Calculate positions
            total_buttons_height = len(button_labels) * button_height + (len(button_labels) - 1) * button_spacing
            try:
                title_space = resolution_manager.get_scaled_spacing(120)
                available_height = game.s_height - title_space
                start_y = title_space + (available_height - total_buttons_height) // 2
            except:
                title_space = 120
                available_height = game.s_height - title_space
                start_y = title_space + (available_height - total_buttons_height) // 2
            
            # Create buttons
            buttons = []
            for i, (label, action) in enumerate(zip(button_labels, button_actions)):
                button_x = (game.s_width - button_width) // 2
                button_y = start_y + i * (button_height + button_spacing)
                
                button = TextButton(label, button_x, button_y, 
                                  font_size=button_font_size, 
                                  width=button_width, height=button_height,
                                  action=action)
                buttons.append(button)
                print(f"      Button '{label}': pos=({button_x}, {button_y}), size={button_width}x{button_height}")
            
            # Test background creation
            from background import Background
            background = Background('background', game.s_width, game.s_height)
            print(f"    ✓ Background created: {background.width}x{background.height}")
            
            # Test title rendering
            from utils import load_font
            title_font = load_font("arial", title_font_size)
            title = title_font.render("Xtreme Cars", True, (255, 255, 255))
            try:
                title_y = resolution_manager.get_scaled_spacing(50)
            except:
                title_y = 50
            title_x = (game.s_width - title.get_width()) // 2
            print(f"    ✓ Title created: pos=({title_x}, {title_y}), size={title.get_width()}x{title.get_height()}")
            
            print(f"    ✓ {name} main menu rendering: PASS")
            
        except Exception as e:
            print(f"    ✗ {name} main menu rendering failed: {e}")
            traceback.print_exc()
        
        finally:
            pygame.quit()
            time.sleep(0.1)


if __name__ == "__main__":
    print("=== Practical Main Menu Resolution Compatibility Test ===")
    
    # Test 1: Resolution manager integration
    rm_ok = test_resolution_manager_integration()
    
    # Test 2: TextButton parameter handling
    tb_ok = test_textbutton_parameters()
    
    # Test 3: Main menu at different resolutions
    test_resolutions = [
        (1024, 768, "1024x768"),
        (1280, 720, "1280x720"),
        (1366, 768, "1366x768"),
        (1920, 1080, "1920x1080"),
        (2560, 1440, "2560x1440"),
        ("fullscreen", "fullscreen", "Fullscreen")
    ]
    
    resolution_results = []
    for width, height, name in test_resolutions:
        result = test_main_menu_at_resolution(width, height, name)
        resolution_results.append((name, result))
    
    # Test 4: Actual main menu rendering
    test_actual_main_menu_rendering()
    
    # Summary
    print("\n=== Test Summary ===")
    print(f"Resolution Manager Integration: {'✓ PASS' if rm_ok else '✗ FAIL'}")
    print(f"TextButton Parameter Handling: {'✓ PASS' if tb_ok else '✗ FAIL'}")
    
    print("\nResolution Tests:")
    for name, result in resolution_results:
        print(f"  {name}: {'✓ PASS' if result else '✗ FAIL'}")
    
    failed_tests = []
    if not rm_ok:
        failed_tests.append("Resolution Manager Integration")
    if not tb_ok:
        failed_tests.append("TextButton Parameter Handling")
    
    failed_resolutions = [name for name, result in resolution_results if not result]
    if failed_resolutions:
        failed_tests.append(f"Resolutions: {', '.join(failed_resolutions)}")
    
    if failed_tests:
        print(f"\n❌ FAILED TESTS: {', '.join(failed_tests)}")
        print("Main menu resolution compatibility has issues that need to be fixed.")
    else:
        print("\n✅ ALL TESTS PASSED")
        print("Main menu resolution compatibility is working correctly.")
