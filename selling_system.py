import pygame
import json
from background import Background
from ui_components import Text<PERSON>utton
from valuation_system import valuation_system
from utils import load_font
from cursor_manager import cursor_manager
from language_manager import get_language_manager

class SellCarDialog:
    def __init__(self, car_data, car_index, usage_data, s_width, s_height):
        self.car_data = car_data
        self.car_index = car_index
        self.usage_data = usage_data

        # Get installed parts that will be sold with the car
        self.installed_parts = get_car_installed_parts(car_index)

        # Adjust dialog size based on number of parts
        self.width = 500
        self.height = 450 + (len(self.installed_parts) * 25)  # Extra height for parts list
        self.rect = pygame.Rect(
            (s_width - self.width) // 2,
            (s_height - self.height) // 2,
            self.width,
            self.height
        )

        # Calculate valuation
        self.valuation = valuation_system.calculate_car_value(car_data, usage_data)
        self.selling_info = valuation_system.estimate_selling_price(car_data, usage_data)

        # Create fonts
        self.header_font = load_font("arial", 32)
        self.font = load_font("arial", 24)
        self.small_font = load_font("arial", 18)

        # Create buttons
        button_y = self.rect.y + self.height - 60
        self.sell_button = pygame.Rect(self.rect.x + 50, button_y, 120, 40)
        self.cancel_button = pygame.Rect(self.rect.x + self.width - 170, button_y, 120, 40)

        # Anti-misclick protection
        self.creation_time = pygame.time.get_ticks()
        self.min_interaction_delay = 500  # 500ms delay before allowing interactions
        self.last_click_time = 0
        self.click_debounce_delay = 200  # 200ms between clicks
    
    def update(self, mouse_pos, mouse_click):
        current_time = pygame.time.get_ticks()

        # Prevent interactions too soon after dialog creation
        if current_time - self.creation_time < self.min_interaction_delay:
            return None

        # Debounce clicks
        if mouse_click[0] and current_time - self.last_click_time > self.click_debounce_delay:
            self.last_click_time = current_time
            if self.sell_button.collidepoint(mouse_pos):
                return "sell"
            elif self.cancel_button.collidepoint(mouse_pos):
                return "cancel"
        return None

    def handle_event(self, event, mouse_pos):
        """Handle mouse events - preferred method"""
        current_time = pygame.time.get_ticks()

        # Prevent interactions too soon after dialog creation
        if current_time - self.creation_time < self.min_interaction_delay:
            return None

        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:  # Left click
            # Debounce clicks
            if current_time - self.last_click_time > self.click_debounce_delay:
                self.last_click_time = current_time
                if self.sell_button.collidepoint(mouse_pos):
                    return "sell"
                elif self.cancel_button.collidepoint(mouse_pos):
                    return "cancel"
        return None

    def draw(self, screen):
        # Dialog background
        pygame.draw.rect(screen, (40, 40, 40), self.rect)
        pygame.draw.rect(screen, (200, 200, 200), self.rect, 2)
        
        # Header
        lang_manager = get_language_manager()
        header_text = self.header_font.render(lang_manager.get_text('selling.vehicle_sale'), True, (255, 255, 255))
        header_x = self.rect.x + (self.width - header_text.get_width()) // 2
        screen.blit(header_text, (header_x, self.rect.y + 20))

        # Car name
        car_name = self.font.render(f"{lang_manager.get_text('selling.model')}: {self.car_data['name'].title()}", True, (200, 200, 200))
        screen.blit(car_name, (self.rect.x + 20, self.rect.y + 70))
        
        # Valuation details
        y_offset = self.rect.y + 110
        
        # Current value
        current_value_text = self.font.render(f"{lang_manager.get_text('selling.current_value')}: {self.valuation['total_value']} {lang_manager.get_text('units.currency')}", True, (255, 255, 255))
        screen.blit(current_value_text, (self.rect.x + 20, y_offset))
        y_offset += 30
        
        # Condition
        condition_percentage = int(self.valuation['car_condition'] * 100)
        condition_category = valuation_system.get_condition_category(self.valuation['car_condition'])
        condition_text = self.font.render(f"{lang_manager.get_text('garage.condition')}: {condition_percentage}% ({condition_category})", True, (200, 200, 200))
        screen.blit(condition_text, (self.rect.x + 20, y_offset))
        y_offset += 30

        # Performance bonus
        performance_text = self.font.render(f"{lang_manager.get_text('selling.performance_bonus')}: {self.valuation['performance_bonus']:.2f}x", True, (200, 200, 200))
        screen.blit(performance_text, (self.rect.x + 20, y_offset))
        y_offset += 40

        # Selling price (highlighted)
        selling_price_text = self.header_font.render(f"{lang_manager.get_text('selling.selling_price')}: {self.selling_info['selling_price']} {lang_manager.get_text('units.currency')}", True, (0, 255, 0))
        screen.blit(selling_price_text, (self.rect.x + 20, y_offset))
        y_offset += 40
        
        # Selling percentage
        percentage_text = self.small_font.render(f"({self.selling_info['selling_percentage']}% {lang_manager.get_text('selling.market_value')})", True, (150, 150, 150))
        screen.blit(percentage_text, (self.rect.x + 20, y_offset))
        y_offset += 30

        # Parts that will be sold with the car
        if self.installed_parts:
            parts_header = self.font.render(f"{lang_manager.get_text('selling.parts_sold_with_car')}:", True, (255, 200, 100))
            screen.blit(parts_header, (self.rect.x + 20, y_offset))
            y_offset += 30

            total_parts_value = 0
            for part in self.installed_parts:
                part_text = self.small_font.render(f"• {part['name']} ({part['category']}) - {part['value']} $", True, (200, 200, 200))
                screen.blit(part_text, (self.rect.x + 30, y_offset))
                y_offset += 25
                total_parts_value += part['value']

            # Show total parts value
            parts_value_text = self.small_font.render(f"{lang_manager.get_text('selling.total_parts_value')}: {total_parts_value} {lang_manager.get_text('units.currency')}", True, (255, 200, 100))
            screen.blit(parts_value_text, (self.rect.x + 30, y_offset))
            y_offset += 30

            # Warning about parts
            warning_parts = self.small_font.render(f"{lang_manager.get_text('selling.warning_parts_sold')}", True, (255, 100, 100))
            screen.blit(warning_parts, (self.rect.x + 20, y_offset))
            y_offset += 25
        else:
            no_parts_text = self.small_font.render(lang_manager.get_text('selling.no_installed_parts'), True, (150, 150, 150))
            screen.blit(no_parts_text, (self.rect.x + 20, y_offset))
            y_offset += 30

        # Check if this is the last car and show warning
        try:
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)
            owned_cars = profile_data.get("inventory", {}).get("owned_cars", [])
            if len(owned_cars) <= 1:
                warning_text = self.small_font.render(lang_manager.get_text('selling.warning_last_car'), True, (255, 100, 100))
                screen.blit(warning_text, (self.rect.x + 20, y_offset))
                y_offset += 25
        except:
            pass

        # Buttons
        current_time = pygame.time.get_ticks()
        buttons_enabled = current_time - self.creation_time >= self.min_interaction_delay

        for button, text, base_color in [(self.sell_button, "Sprzedaj", (0, 200, 0)), (self.cancel_button, "Anuluj", (200, 0, 0))]:
            # Dim buttons if not yet enabled
            if buttons_enabled:
                color = base_color
                text_color = (255, 255, 255)
            else:
                color = tuple(c // 3 for c in base_color)  # Darker version
                text_color = (150, 150, 150)

            pygame.draw.rect(screen, color, button)
            pygame.draw.rect(screen, (255, 255, 255), button, 2)

            button_text = self.font.render(text, True, text_color)
            text_x = button.x + (button.width - button_text.get_width()) // 2
            text_y = button.y + (button.height - button_text.get_height()) // 2
            screen.blit(button_text, (text_x, text_y))

        # Show countdown if buttons are not yet enabled
        if not buttons_enabled:
            remaining_time = (self.min_interaction_delay - (current_time - self.creation_time)) / 1000.0
            countdown_text = self.small_font.render(f"{lang_manager.get_text('selling.wait_before_click', time=remaining_time)}", True, (255, 200, 100))
            text_x = self.rect.x + (self.width - countdown_text.get_width()) // 2
            text_y = self.rect.y + self.height - 100
            screen.blit(countdown_text, (text_x, text_y))

def get_car_installed_parts(car_index):
    """Get list of parts that will be sold with the car"""
    try:
        # Load garage data
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)

        if car_index < 0 or car_index >= len(garage_data):
            return []

        car_data = garage_data[car_index]
        installed_parts = car_data.get("parts", {})

        parts_list = []
        for part_category, part_data in installed_parts.items():
            if part_data is not None and part_data.get("name"):
                parts_list.append({
                    "name": part_data["name"],
                    "category": part_category,
                    "value": part_data.get("value", 0)
                })

        return parts_list
    except Exception:
        return []

def sell_car(car_index):
    """Sell a car and update profile"""
    try:
        # Load profile data
        with open('data/profile.json', 'r') as f:
            profile_data = json.load(f)

        # Load garage data
        with open('data/garage.json', 'r') as f:
            garage_data = json.load(f)

        # Check if car exists
        owned_cars = profile_data.get("inventory", {}).get("owned_cars", [])
        if car_index < 0 or car_index >= len(owned_cars):
            lang_manager = get_language_manager()
            return False, lang_manager.get_text('selling.invalid_car_index')

        # Allow selling the last car, but warn the user
        is_last_car = len(owned_cars) <= 1

        # Get car data
        car_name = owned_cars[car_index]
        car_data = garage_data[car_index]

        # Get usage data
        usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(car_index), valuation_system.get_default_usage_data())

        # Calculate selling price
        selling_info = valuation_system.estimate_selling_price(car_data, usage_data)
        selling_price = selling_info["selling_price"]

        # CRITICAL FIX: Remove installed parts from player inventory
        # This prevents the exploit where players keep parts after selling the car
        installed_parts = car_data.get("parts", {})
        owned_parts = profile_data.get("inventory", {}).get("owned_parts", {})

        for part_category, part_data in installed_parts.items():
            if part_data is not None and part_data.get("name"):
                part_name = part_data["name"]
                # Remove the part from player's inventory if it exists
                if part_category in owned_parts and part_name in owned_parts[part_category]:
                    owned_parts[part_category].remove(part_name)

        # Update profile
        # Remove car from owned cars
        owned_cars.pop(car_index)
        profile_data["inventory"]["owned_cars"] = owned_cars

        # Remove car from garage
        garage_data.pop(car_index)

        # Remove usage data
        if "usage_data" in profile_data and "cars" in profile_data["usage_data"]:
            if str(car_index) in profile_data["usage_data"]["cars"]:
                del profile_data["usage_data"]["cars"][str(car_index)]

            # Reindex remaining cars
            new_usage_data = {}
            for i, (old_index, usage) in enumerate(profile_data["usage_data"]["cars"].items()):
                if int(old_index) > car_index:
                    new_usage_data[str(int(old_index) - 1)] = usage
                elif int(old_index) < car_index:
                    new_usage_data[old_index] = usage
            profile_data["usage_data"]["cars"] = new_usage_data
        
        # Remove car colors
        if "car_colors" in profile_data["cars"]:
            if str(car_index) in profile_data["cars"]["car_colors"]:
                del profile_data["cars"]["car_colors"][str(car_index)]
            
            # Reindex remaining car colors
            new_car_colors = {}
            for old_index, color in profile_data["cars"]["car_colors"].items():
                if int(old_index) > car_index:
                    new_car_colors[str(int(old_index) - 1)] = color
                elif int(old_index) < car_index:
                    new_car_colors[old_index] = color
            profile_data["cars"]["car_colors"] = new_car_colors
        
        # Update selected car if necessary
        selected_car = profile_data["cars"].get("selected_car", 0)
        if selected_car >= car_index:
            new_selected = max(0, selected_car - 1)
            # If this was the last car, set selected_car to -1 to indicate no cars
            if len(owned_cars) == 0:
                new_selected = -1
            profile_data["cars"]["selected_car"] = new_selected
        
        # Add money
        profile_data["money"] += selling_price
        
        # Save files
        with open('data/profile.json', 'w') as f:
            json.dump(profile_data, f, indent=4)
        
        with open('data/garage.json', 'w') as f:
            json.dump(garage_data, f, indent=4)
        
        # Auto-save if current save slot is set
        from save_system import save_system
        if save_system.current_save_slot:
            save_system.save_game(save_system.current_save_slot)
        
        lang_manager = get_language_manager()
        return True, f"{lang_manager.get_text('selling.sold')} {car_name} {lang_manager.get_text('selling.for')} {selling_price} {lang_manager.get_text('units.currency')}"
        
    except Exception as e:
        lang_manager = get_language_manager()
        return False, f"{lang_manager.get_text('selling.error_during_sale')}: {str(e)}"

def draw_garage_with_selling(s_width, s_height, screen):
    """Enhanced garage screen with selling functionality"""
    run = True
    bg = Background('background', s_width, s_height)
    
    # Load data
    with open('data/garage.json') as f:
        cars_data = json.load(f)
    with open('data/profile.json') as f:
        profile_data = json.load(f)
    
    # Create buttons
    lang_manager = get_language_manager()
    back_button = TextButton(lang_manager.get_text('menu.back'), 50, 50, font_size=36)

    # Create fonts
    header_font = load_font("arial", 48)
    info_font = load_font("arial", 24)

    header = header_font.render(f"{lang_manager.get_text('garage.garage')} - {lang_manager.get_text('selling.sale')}", True, (255, 255, 255))
    
    # Dialog state
    sell_dialog = None
    message = ""
    message_timer = 0
    
    while run:
        mouse_pos = pygame.mouse.get_pos()

        # Reset cursor state for this frame
        cursor_manager.reset_frame()

        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                if sell_dialog:
                    sell_dialog = None
                else:
                    return

            # Handle dialog events
            if sell_dialog:
                if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                    dialog_result = sell_dialog.handle_event(event, mouse_pos)
                    if dialog_result == "sell":
                        success, msg = sell_car(sell_dialog.car_index)
                        message = msg
                        message_timer = 180  # 3 seconds at 60 FPS
                        sell_dialog = None

                        # Reload data after selling
                        with open('data/garage.json') as f:
                            cars_data = json.load(f)
                        with open('data/profile.json') as f:
                            profile_data = json.load(f)

                    elif dialog_result == "cancel":
                        sell_dialog = None
            else:
                # Handle button events
                if back_button.handle_event(event, mouse_pos):
                    return

                # Handle sell button clicks
                if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                    owned_cars = profile_data.get("inventory", {}).get("owned_cars", [])

                    if owned_cars:
                        # Check sell button clicks
                        cards_per_row = 3
                        card_width = 250
                        card_height = 200
                        card_spacing = 30
                        start_x = (s_width - (cards_per_row * card_width + (cards_per_row - 1) * card_spacing)) // 2
                        start_y = 150

                        for i, car_name in enumerate(owned_cars):
                            if i < len(cars_data):
                                row = i // cards_per_row
                                col = i % cards_per_row
                                x = start_x + col * (card_width + card_spacing)
                                y = start_y + row * (card_height + card_spacing)

                                # Sell button
                                sell_button_rect = pygame.Rect(x + 10, y + 140, 100, 30)

                                if sell_button_rect.collidepoint(mouse_pos):
                                    # Add a small delay to prevent accidental clicks
                                    current_time = pygame.time.get_ticks()
                                    if not hasattr(sell_car, 'last_sell_button_click'):
                                        sell_car.last_sell_button_click = 0

                                    if current_time - sell_car.last_sell_button_click > 300:  # 300ms debounce
                                        sell_car.last_sell_button_click = current_time
                                        car_data = cars_data[i]
                                        usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(i), valuation_system.get_default_usage_data())
                                        sell_dialog = SellCarDialog(car_data, i, usage_data, s_width, s_height)
                                    break

        # Update message timer
        if message_timer > 0:
            message_timer -= 1
            if message_timer <= 0:
                message = ""

        # Update button hover states
        if not sell_dialog:
            back_button.update(mouse_pos)

        # Handle dialog hover states
        if sell_dialog:
            sell_dialog.update(mouse_pos, [False, False, False])  # No clicks through legacy method
        
        # Draw everything
        bg.draw(screen)
        
        # Draw header
        screen.blit(header, (s_width // 2 - header.get_width() // 2, 50))
        
        # Draw owned cars
        owned_cars = profile_data.get("inventory", {}).get("owned_cars", [])
        
        if not owned_cars:
            no_cars_text = info_font.render(lang_manager.get_text('selling.no_cars_in_garage'), True, (200, 200, 200))
            screen.blit(no_cars_text, (s_width // 2 - no_cars_text.get_width() // 2, 300))
        else:
            # Draw car cards
            cards_per_row = 3
            card_width = 250
            card_height = 200
            card_spacing = 30
            start_x = (s_width - (cards_per_row * card_width + (cards_per_row - 1) * card_spacing)) // 2
            start_y = 150
            
            for i, car_name in enumerate(owned_cars):
                if i < len(cars_data):
                    car_data = cars_data[i]
                    
                    row = i // cards_per_row
                    col = i % cards_per_row
                    x = start_x + col * (card_width + card_spacing)
                    y = start_y + row * (card_height + card_spacing)
                    
                    # Car card background
                    card_rect = pygame.Rect(x, y, card_width, card_height)
                    pygame.draw.rect(screen, (60, 60, 60), card_rect)
                    pygame.draw.rect(screen, (200, 200, 200), card_rect, 2)
                    
                    # Car info
                    car_name_text = info_font.render(f"{car_data['name'].title()}", True, (255, 255, 255))
                    screen.blit(car_name_text, (x + 10, y + 10))
                    
                    # Get usage data and calculate value
                    usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(i), valuation_system.get_default_usage_data())
                    valuation = valuation_system.calculate_car_value(car_data, usage_data)
                    selling_info = valuation_system.estimate_selling_price(car_data, usage_data)
                    
                    # Value info
                    value_text = info_font.render(f"{lang_manager.get_text('garage.value')}: {valuation['total_value']} {lang_manager.get_text('units.currency')}", True, (200, 200, 200))
                    screen.blit(value_text, (x + 10, y + 40))

                    selling_text = info_font.render(f"{lang_manager.get_text('selling.sale')}: {selling_info['selling_price']} {lang_manager.get_text('units.currency')}", True, (0, 255, 0))
                    screen.blit(selling_text, (x + 10, y + 70))

                    condition_percentage = int(valuation['car_condition'] * 100)
                    condition_text = info_font.render(f"{lang_manager.get_text('garage.condition')}: {condition_percentage}%", True, (200, 200, 200))
                    screen.blit(condition_text, (x + 10, y + 100))
                    
                    # Sell button
                    sell_button_rect = pygame.Rect(x + 10, y + 140, 100, 30)
                    
                    # Allow selling any car (including the last one)
                    can_sell = True
                    button_color = (200, 0, 0) if can_sell else (100, 100, 100)
                    text_color = (255, 255, 255) if can_sell else (150, 150, 150)
                    
                    pygame.draw.rect(screen, button_color, sell_button_rect)
                    pygame.draw.rect(screen, (255, 255, 255), sell_button_rect, 1)
                    
                    sell_text = info_font.render("Sprzedaj", True, text_color)
                    text_x = sell_button_rect.x + (sell_button_rect.width - sell_text.get_width()) // 2
                    text_y = sell_button_rect.y + (sell_button_rect.height - sell_text.get_height()) // 2
                    screen.blit(sell_text, (text_x, text_y))
        
        # Draw instructions
        if len(owned_cars) > 1:
            instruction = info_font.render(f"{lang_manager.get_text('selling.click_sell_instruction')}", True, (200, 200, 200))
        elif len(owned_cars) == 1:
            instruction = info_font.render(lang_manager.get_text('selling.warning_last_car_sale'), True, (255, 200, 100))
        else:
            instruction = info_font.render(lang_manager.get_text('selling.no_cars_owned'), True, (255, 100, 100))
        
        screen.blit(instruction, (s_width // 2 - instruction.get_width() // 2, s_height - 100))
        
        # Draw message
        if message:
            message_text = info_font.render(message, True, (255, 255, 0))
            message_bg = pygame.Rect(
                s_width // 2 - message_text.get_width() // 2 - 10,
                s_height - 150,
                message_text.get_width() + 20,
                message_text.get_height() + 10
            )
            pygame.draw.rect(screen, (40, 40, 40), message_bg)
            pygame.draw.rect(screen, (255, 255, 0), message_bg, 2)
            screen.blit(message_text, (message_bg.x + 10, message_bg.y + 5))
        
        # Draw buttons
        back_button.draw(screen)
        
        # Draw dialog if active
        if sell_dialog:
            # Draw overlay
            overlay = pygame.Surface((s_width, s_height))
            overlay.set_alpha(128)
            overlay.fill((0, 0, 0))
            screen.blit(overlay, (0, 0))
            sell_dialog.draw(screen)

        # Update cursor based on hover states
        cursor_manager.update_cursor()

        pygame.display.update()
    
    return
