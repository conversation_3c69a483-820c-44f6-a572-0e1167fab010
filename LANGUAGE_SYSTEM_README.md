# System Językowy - Xtra Cars

## Prz<PERSON>ląd

Zaimplementowano kompletny system wyboru języka w grze Xtra Cars z obsługą czterech języków:
- **<PERSON><PERSON> (pl)** - j<PERSON><PERSON><PERSON> domyślny
- **<PERSON><PERSON><PERSON> (en)**
- **<PERSON><PERSON><PERSON><PERSON> (de)**
- **<PERSON><PERSON><PERSON><PERSON> (es)**

## Funkcjonalności

### ✅ Zaimplementowane funkcje

1. **Menedżer języków** (`language_manager.py`)
   - Automatyczne ładowanie wszystkich plików językowych
   - Przełączanie między językami w czasie rzeczywistym
   - Fallback do języka polskiego w przypadku brakujących tłumaczeń
   - Formatowanie tekstów z parametrami (np. rozdzielczość)

2. **Pliki językowe** (`data/languages/`)
   - `pl.json` - <PERSON><PERSON> (domyślny)
   - `en.json` - Angie<PERSON><PERSON>
   - `de.json` - <PERSON><PERSON><PERSON>cki
   - `es.json` - <PERSON><PERSON><PERSON><PERSON>

3. **Komponent UI** (`ui_components.py`)
   - `DropdownButton` - lista rozwijana do wyboru języka
   - Obsługa zdarzeń myszy i hover
   - Responsywny design

4. **Integracja z menu ustawień**
   - Opcja wyboru języka w menu "Ustawienia Gry"
   - Natychmiastowe przełączanie języka
   - Komunikat potwierdzający zmianę

5. **Trwałość ustawień**
   - Zapisywanie wybranego języka w `profile.json`
   - Automatyczne ładowanie języka przy starcie gry
   - Zachowanie ustawień między sesjami

## Struktura plików

```
data/languages/
├── pl.json          # Tłumaczenia polskie
├── en.json          # Tłumaczenia angielskie
├── de.json          # Tłumaczenia niemieckie
└── es.json          # Tłumaczenia hiszpańskie

language_manager.py  # Główny menedżer języków
ui_components.py     # Komponent DropdownButton
game.py             # Integracja z główną grą
```

## Użycie

### Podstawowe użycie

```python
from language_manager import get_text, set_language

# Pobranie tłumaczenia
text = get_text("menu.main_menu")

# Zmiana języka
set_language("en")

# Formatowanie z parametrami
resolution_text = get_text("settings.current_resolution", width=1920, height=1080)
```

### Dodawanie nowych tłumaczeń

1. Dodaj klucz do wszystkich plików językowych w `data/languages/`
2. Użyj `get_text("kategoria.klucz")` w kodzie

Przykład:
```json
{
    "menu": {
        "new_option": "Nowa opcja"
    }
}
```

```python
text = get_text("menu.new_option")
```

## Kategorie tłumaczeń

### `menu`
- Główne menu i podmenu
- Przyciski nawigacyjne

### `game`
- Opcje gry (Single Player, Load Game, etc.)
- Profile użytkownika

### `settings`
- Ustawienia gry
- Opcje konfiguracji

### `garage`
- Garaż i tuning
- Informacje o samochodach

### `shop`
- Sklep i handel
- Kupno/sprzedaż

### `race`
- Wyścigi i rywalizacja
- Statystyki

### `parts`
- Części samochodowe
- Kompatybilność

### `messages`
- Komunikaty systemowe
- Błędy i potwierdzenia

### `ui`
- Elementy interfejsu
- Przyciski akcji

### `units`
- Jednostki miary
- Skróty

## Testowanie

Dostępne są dwa skrypty testowe:

### `test_language_system.py`
- Testuje podstawową funkcjonalność menedżera języków
- Sprawdza wszystkie pliki językowe
- Testuje integrację z profilem

### `test_language_integration.py`
- Testuje integrację z główną grą
- Sprawdza komponenty UI
- Testuje zapisywanie/ładowanie ustawień

Uruchomienie testów:
```bash
python test_language_system.py
python test_language_integration.py
```

## Jak używać w grze

1. **Uruchom grę**
2. **Przejdź do menu głównego**
3. **Wybierz "Ustawienia"**
4. **Wybierz "Ustawienia gry"**
5. **Kliknij na listę rozwijaną języków**
6. **Wybierz preferowany język**
7. **Język zostanie natychmiast zmieniony i zapisany**

## Rozszerzanie systemu

### Dodawanie nowego języka

1. Utwórz nowy plik `data/languages/[kod].json`
2. Skopiuj strukturę z istniejącego pliku
3. Przetłumacz wszystkie teksty
4. Dodaj język do `available_languages` w `language_manager.py`

### Dodawanie nowych kategorii

1. Dodaj nową sekcję do wszystkich plików językowych
2. Użyj `get_text("nowa_kategoria.klucz")` w kodzie

## Uwagi techniczne

- System używa UTF-8 dla obsługi znaków specjalnych
- Fallback do języka polskiego zapewnia stabilność
- Wszystkie tłumaczenia są ładowane przy starcie dla wydajności
- Ustawienia języka są zapisywane w sekcji `settings` profilu użytkownika

## Rozszerzone tłumaczenia

System został znacznie rozszerzony o dodatkowe tłumaczenia dla wszystkich aspektów gry:

### Dodatkowe kategorie tłumaczeń:

**Garaż (`garage`)**
- `car_selection`, `tuning_garage`, `select_car`
- `install_part`, `remove_part`, `part_installed`, `part_removed`
- `no_compatible_parts`, `part_compatibility`

**Sklep (`shop`)**
- `purchase`, `sale`, `price`, `quantity`, `total`
- `insufficient_funds`, `purchase_successful`, `sale_successful`
- `confirm_purchase`, `confirm_sale`, `cancel_transaction`

**Wyścigi (`race`)**
- `race_info`, `start_race`, `race_results`
- `you_won`, `you_lost`, `race_time`, `best_time`
- `position`, `countdown`, `ready`, `go`

**Części (`parts`)**
- `part_name`, `part_type`, `part_category`
- `installation`, `removal`, `upgrade`, `downgrade`
- `performance`, `durability`, `cost`, `benefit`

**Komunikaty (`messages`)**
- `part_installed_successfully`, `part_removed_successfully`
- `insufficient_money`, `car_purchased`, `part_purchased`
- `save_successful`, `load_successful`, `operation_cancelled`
- `confirm_action`, `action_completed`, `error_occurred`

**Interfejs (`ui`)**
- `apply`, `reset`, `default`, `custom`, `auto`, `manual`
- `enabled`, `disabled`, `on`, `off`, `high`, `medium`, `low`
- `refresh`, `update`, `upgrade`, `install`, `uninstall`, `repair`

## Status implementacji

✅ **UKOŃCZONE** - System językowy jest w pełni funkcjonalny i przetestowany

Wszystkie zaplanowane funkcje zostały zaimplementowane i przetestowane:
- ✅ Pliki językowe (pl, en, de, es) - **ROZSZERZONE**
- ✅ Menedżer języków
- ✅ Komponent UI (DropdownButton)
- ✅ Integracja z menu ustawień
- ✅ Zapisywanie/ładowanie preferencji
- ✅ Tłumaczenie interfejsu użytkownika - **ROZSZERZONE**
- ✅ Testy funkcjonalności - **ROZSZERZONE**
- ✅ **NOWE**: Ponad 200 dodatkowych tłumaczeń dla wszystkich języków
- ✅ **NOWE**: Kompleksowe pokrycie wszystkich elementów UI
- ✅ **NOWE**: Rozszerzone testy tłumaczeń
