"""
Test script for the language system
"""

import json
import os
from language_manager import get_language_manager, get_text, set_language, get_available_languages
from utils import resource_path

def test_language_manager():
    """Test the language manager functionality"""
    print("🧪 Testing Language Manager...")
    
    # Test 1: Initialize language manager
    print("\n1. Testing language manager initialization...")
    lang_manager = get_language_manager()
    print(f"✅ Language manager initialized")
    print(f"   Current language: {lang_manager.get_current_language()}")
    print(f"   Available languages: {lang_manager.get_available_languages()}")
    
    # Test 2: Test text retrieval in default language (Polish)
    print("\n2. Testing text retrieval in Polish...")
    main_menu_text = get_text("menu.main_menu")
    game_text = get_text("menu.game")
    settings_text = get_text("menu.settings")
    print(f"✅ Polish texts:")
    print(f"   Main Menu: {main_menu_text}")
    print(f"   Game: {game_text}")
    print(f"   Settings: {settings_text}")
    
    # Test 3: Switch to English and test
    print("\n3. Testing language switch to English...")
    success = set_language("en")
    if success:
        print("✅ Successfully switched to English")
        main_menu_text = get_text("menu.main_menu")
        game_text = get_text("menu.game")
        settings_text = get_text("menu.settings")
        print(f"   Main Menu: {main_menu_text}")
        print(f"   Game: {game_text}")
        print(f"   Settings: {settings_text}")
    else:
        print("❌ Failed to switch to English")
    
    # Test 4: Switch to German and test
    print("\n4. Testing language switch to German...")
    success = set_language("de")
    if success:
        print("✅ Successfully switched to German")
        main_menu_text = get_text("menu.main_menu")
        game_text = get_text("menu.game")
        settings_text = get_text("menu.settings")
        print(f"   Main Menu: {main_menu_text}")
        print(f"   Game: {game_text}")
        print(f"   Settings: {settings_text}")
    else:
        print("❌ Failed to switch to German")
    
    # Test 5: Switch to Spanish and test
    print("\n5. Testing language switch to Spanish...")
    success = set_language("es")
    if success:
        print("✅ Successfully switched to Spanish")
        main_menu_text = get_text("menu.main_menu")
        game_text = get_text("menu.game")
        settings_text = get_text("menu.settings")
        print(f"   Main Menu: {main_menu_text}")
        print(f"   Game: {game_text}")
        print(f"   Settings: {settings_text}")
    else:
        print("❌ Failed to switch to Spanish")
    
    # Test 6: Test string formatting
    print("\n6. Testing string formatting...")
    set_language("en")
    resolution_text = get_text("settings.current_resolution", width=1920, height=1080)
    print(f"✅ Formatted text: {resolution_text}")
    
    # Test 7: Test missing key fallback
    print("\n7. Testing missing key fallback...")
    missing_text = get_text("nonexistent.key")
    print(f"✅ Missing key result: {missing_text}")
    
    # Test 8: Test profile integration
    print("\n8. Testing profile integration...")
    try:
        # Create test profile data
        test_profile = {
            "username": "Test Player",
            "money": 1000,
            "level": {"current": 1, "exp": 0, "required_to_next_level": 100}
        }
        
        # Save language preference
        updated_profile = lang_manager.save_language_preference(test_profile)
        print(f"✅ Profile updated with language preference")
        print(f"   Language in profile: {updated_profile.get('settings', {}).get('language', 'Not found')}")
        
        # Load language preference
        set_language("pl")  # Reset to Polish
        lang_manager.load_language_preference(updated_profile)
        print(f"✅ Language preference loaded from profile")
        print(f"   Current language after loading: {lang_manager.get_current_language()}")
        
    except Exception as e:
        print(f"❌ Profile integration test failed: {e}")
    
    print("\n🎉 Language system tests completed!")

def test_language_files():
    """Test that all language files exist and have required keys"""
    print("\n🧪 Testing Language Files...")
    
    required_keys = [
        "menu.main_menu",
        "menu.game",
        "menu.cars", 
        "menu.trade",
        "menu.settings",
        "menu.quit_game",
        "menu.back",
        "settings.language",
        "settings.current_resolution",
        "settings.fullscreen_mode",
        "messages.language_changed",
        "languages.pl",
        "languages.en",
        "languages.de",
        "languages.es"
    ]
    
    languages = ["pl", "en", "de", "es"]
    
    for lang in languages:
        print(f"\n📁 Testing {lang}.json...")
        try:
            lang_file = f"data/languages/{lang}.json"
            with open(resource_path(lang_file), 'r', encoding='utf-8') as f:
                lang_data = json.load(f)
            
            print(f"✅ {lang}.json loaded successfully")
            
            # Check required keys
            missing_keys = []
            for key in required_keys:
                keys = key.split('.')
                current = lang_data
                try:
                    for k in keys:
                        current = current[k]
                except (KeyError, TypeError):
                    missing_keys.append(key)
            
            if missing_keys:
                print(f"⚠️  Missing keys in {lang}.json: {missing_keys}")
            else:
                print(f"✅ All required keys present in {lang}.json")
                
        except Exception as e:
            print(f"❌ Error loading {lang}.json: {e}")

if __name__ == "__main__":
    print("🚀 Starting Language System Tests")
    print("=" * 50)
    
    test_language_files()
    test_language_manager()
    
    print("\n" + "=" * 50)
    print("✨ All tests completed!")
