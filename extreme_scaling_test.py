"""
Test extreme resolution differences to make scaling more obvious.
This will help identify if scaling is actually being applied visually.
"""

import pygame
import sys
import time
from game import Game
from background import Background
from ui_components import TextButton
from utils import load_font
from resolution_manager import get_resolution_manager


def test_extreme_resolution_difference():
    """Test with very different resolutions to make scaling obvious"""
    print("🔍 TESTING EXTREME RESOLUTION DIFFERENCES")
    print("=" * 50)
    
    # Test with very different resolutions
    test_resolutions = [
        (800, 600, "Tiny (800x600)"),
        (1920, 1080, "Large (1920x1080)")
    ]
    
    results = []
    
    for width, height, name in test_resolutions:
        print(f"\n📐 Testing {name}")
        print("-" * 30)
        
        try:
            pygame.init()
            pygame.font.init()
            
            # Create game and set resolution
            game = Game()
            game.change_resolution(width, height)
            
            actual_width, actual_height = game.s_width, game.s_height
            print(f"Actual screen size: {actual_width}x{actual_height}")
            
            # Get resolution manager
            resolution_manager = get_resolution_manager()
            
            # Get scaling values
            button_font_size = resolution_manager.get_scaled_font_size(36)
            button_width, button_height = resolution_manager.get_scaled_button_size(250, 60)
            button_spacing = resolution_manager.get_scaled_spacing(75)
            title_font_size = resolution_manager.get_scaled_font_size(48)
            
            print(f"📏 Calculated scaling values:")
            print(f"  Button font: {button_font_size}px")
            print(f"  Button size: {button_width}x{button_height}px")
            print(f"  Button spacing: {button_spacing}px")
            print(f"  Title font: {title_font_size}px")
            
            # Create actual UI elements to test rendering
            button_labels = ['Gra', 'Samochody', 'Handel', 'Ustawienia', 'Wyjdź z gry']
            
            # Calculate positions
            total_buttons_height = len(button_labels) * button_height + (len(button_labels) - 1) * button_spacing
            title_space = resolution_manager.get_scaled_spacing(120)
            available_height = actual_height - title_space
            start_y = title_space + (available_height - total_buttons_height) // 2
            
            # Create buttons and test actual rendering
            buttons = []
            for i, label in enumerate(button_labels):
                button_x = (actual_width - button_width) // 2
                button_y = start_y + i * (button_height + button_spacing)
                
                button = TextButton(label, button_x, button_y, 
                                  font_size=button_font_size, 
                                  width=button_width, height=button_height,
                                  action=lambda: None)
                buttons.append(button)
                
                # Check actual button properties
                print(f"  Button '{label}':")
                print(f"    Position: ({button.x}, {button.y})")
                print(f"    Font size stored: {button.font_size}px")
                print(f"    Width/Height stored: {button.width}x{button.height}px")
                print(f"    Actual surface size: {button.surface.get_width()}x{button.surface.get_height()}px")
            
            # Test title rendering
            title_font = load_font("arial", title_font_size)
            title_surface = title_font.render("Xtreme Cars", True, (255, 255, 255))
            title_y = resolution_manager.get_scaled_spacing(50)
            title_x = (actual_width - title_surface.get_width()) // 2
            
            print(f"  Title:")
            print(f"    Font size: {title_font_size}px")
            print(f"    Position: ({title_x}, {title_y})")
            print(f"    Rendered size: {title_surface.get_width()}x{title_surface.get_height()}px")
            
            # Render everything and save screenshot
            background = Background('background', actual_width, actual_height)
            background.draw(game.screen)
            game.screen.blit(title_surface, (title_x, title_y))
            
            for button in buttons:
                button.draw(game.screen)
            
            # Save screenshot
            screenshot_name = f"extreme_test_{name.lower().replace(' ', '_').replace('(', '').replace(')', '')}.png"
            pygame.image.save(game.screen, screenshot_name)
            print(f"📸 Screenshot saved: {screenshot_name}")
            
            results.append({
                'name': name,
                'resolution': f"{actual_width}x{actual_height}",
                'button_font': button_font_size,
                'button_size': f"{button_width}x{button_height}",
                'title_font': title_font_size,
                'title_rendered_size': f"{title_surface.get_width()}x{title_surface.get_height()}",
                'first_button_surface_size': f"{buttons[0].surface.get_width()}x{buttons[0].surface.get_height()}",
                'screenshot': screenshot_name
            })
            
            pygame.quit()
            time.sleep(0.5)
            
        except Exception as e:
            print(f"❌ Error testing {name}: {e}")
            import traceback
            traceback.print_exc()
            pygame.quit()
    
    # Compare results
    if len(results) == 2:
        small = results[0]
        large = results[1]
        
        print(f"\n📊 EXTREME SCALING COMPARISON")
        print("=" * 50)
        print(f"{'Aspect':<25} {'Tiny (800x600)':<20} {'Large (1920x1080)':<20} {'Difference'}")
        print("-" * 80)
        
        # Button font comparison
        small_font = small['button_font']
        large_font = large['button_font']
        font_diff = large_font - small_font
        font_ratio = large_font / small_font if small_font > 0 else 0
        print(f"{'Button Font Size':<25} {small_font}px{'':<15} {large_font}px{'':<15} +{font_diff}px ({font_ratio:.2f}x)")
        
        # Title font comparison
        small_title = small['title_font']
        large_title = large['title_font']
        title_diff = large_title - small_title
        title_ratio = large_title / small_title if small_title > 0 else 0
        print(f"{'Title Font Size':<25} {small_title}px{'':<15} {large_title}px{'':<15} +{title_diff}px ({title_ratio:.2f}x)")
        
        # Button size comparison
        print(f"{'Button Dimensions':<25} {small['button_size']:<20} {large['button_size']:<20}")
        
        # Rendered sizes
        print(f"{'Title Rendered Size':<25} {small['title_rendered_size']:<20} {large['title_rendered_size']:<20}")
        print(f"{'Button Surface Size':<25} {small['first_button_surface_size']:<20} {large['first_button_surface_size']:<20}")
        
        print(f"\n💡 ANALYSIS:")
        if font_diff > 5:  # Significant difference
            print(f"✅ SCALING IS WORKING: Font sizes differ by {font_diff}px ({font_ratio:.2f}x)")
            print(f"✅ This should be VISUALLY NOTICEABLE")
        else:
            print(f"⚠️  SCALING DIFFERENCE SMALL: Only {font_diff}px difference")
            print(f"❓ This might not be visually obvious")
        
        print(f"\n📁 Compare these screenshots:")
        print(f"  • {small['screenshot']}")
        print(f"  • {large['screenshot']}")
        print(f"  Look for differences in button and text sizes!")
    
    return results


def test_actual_game_resolution_changes():
    """Test resolution changes in the actual game flow"""
    print(f"\n🎮 TESTING ACTUAL GAME RESOLUTION CHANGES")
    print("=" * 50)
    
    pygame.init()
    pygame.font.init()
    
    # Start game
    game = Game()
    
    # Test sequence: start at high res, change to low res, back to high res
    test_sequence = [
        (1920, 1080, "Start High"),
        (800, 600, "Change to Low"),
        (1920, 1080, "Back to High")
    ]
    
    for i, (width, height, description) in enumerate(test_sequence):
        print(f"\n{i+1}. {description} ({width}x{height})")
        print("-" * 30)
        
        # Change resolution
        game.change_resolution(width, height)
        
        # Get current scaling
        resolution_manager = get_resolution_manager()
        button_font = resolution_manager.get_scaled_font_size(36)
        button_size = resolution_manager.get_scaled_button_size(250, 60)
        
        print(f"Screen size: {game.s_width}x{game.s_height}")
        print(f"Button font: {button_font}px")
        print(f"Button size: {button_size}")
        
        # Create a test button to verify actual rendering
        test_button = TextButton("Test", 100, 100, 
                                font_size=button_font, 
                                width=button_size[0], height=button_size[1])
        
        print(f"Test button surface size: {test_button.surface.get_width()}x{test_button.surface.get_height()}px")
        
        time.sleep(1)  # Brief pause
    
    pygame.quit()
    print(f"\n✅ Game resolution change test completed")


if __name__ == "__main__":
    print("🔬 EXTREME SCALING VERIFICATION")
    print("=" * 40)
    
    try:
        # Test extreme differences
        results = test_extreme_resolution_difference()
        
        # Test actual game flow
        test_actual_game_resolution_changes()
        
        print(f"\n🎯 SUMMARY:")
        print("If scaling is working, you should see:")
        print("✅ Different font sizes in the comparison table")
        print("✅ Different button dimensions")
        print("✅ Visually different screenshots")
        print("❌ If all values are identical, scaling is NOT working")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
