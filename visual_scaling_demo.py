"""
Visual demonstration of main menu scaling at different resolutions.
This script creates side-by-side comparisons to show scaling differences.
"""

import pygame
import sys
import time
from game import Game
from background import Background
from ui_components import TextButton
from utils import load_font
from resolution_manager import get_resolution_manager


def capture_main_menu_at_resolution(width, height, resolution_name):
    """Capture main menu appearance at specific resolution"""
    print(f"\n📸 Capturing main menu at {resolution_name} ({width}x{height})")
    
    try:
        pygame.init()
        pygame.font.init()
        
        # Create game and set resolution
        game = Game()
        if width == "fullscreen":
            game.change_resolution("fullscreen", "fullscreen")
        else:
            game.change_resolution(width, height)
        
        actual_width, actual_height = game.s_width, game.s_height
        print(f"  Actual screen size: {actual_width}x{actual_height}")
        
        # Get resolution manager
        resolution_manager = get_resolution_manager()
        
        # Get scaling values
        button_font_size = resolution_manager.get_scaled_font_size(36)
        button_width, button_height = resolution_manager.get_scaled_button_size(250, 60)
        button_spacing = resolution_manager.get_scaled_spacing(75)
        title_font_size = resolution_manager.get_scaled_font_size(48)
        title_space = resolution_manager.get_scaled_spacing(120)
        
        print(f"  📏 Scaling measurements:")
        print(f"    Button font size: {button_font_size}px")
        print(f"    Button dimensions: {button_width}x{button_height}px")
        print(f"    Button spacing: {button_spacing}px")
        print(f"    Title font size: {title_font_size}px")
        print(f"    Title space: {title_space}px")
        
        # Create main menu elements
        button_labels = ['Gra', 'Samochody', 'Handel', 'Ustawienia', 'Wyjdź z gry']
        buttons = []
        
        # Calculate positions
        total_buttons_height = len(button_labels) * button_height + (len(button_labels) - 1) * button_spacing
        available_height = actual_height - title_space
        start_y = title_space + (available_height - total_buttons_height) // 2
        
        print(f"  📐 Positioning calculations:")
        print(f"    Total buttons height: {total_buttons_height}px")
        print(f"    Available height: {available_height}px")
        print(f"    Start Y position: {start_y}px")
        
        for i, label in enumerate(button_labels):
            button_x = (actual_width - button_width) // 2
            button_y = start_y + i * (button_height + button_spacing)
            
            button = TextButton(label, button_x, button_y, 
                              font_size=button_font_size, 
                              width=button_width, height=button_height,
                              action=lambda: None)
            buttons.append(button)
            print(f"    Button '{label}': pos=({button_x}, {button_y})")
        
        # Create background and title
        background = Background('background', actual_width, actual_height)
        title_font = load_font("arial", title_font_size)
        title = title_font.render("Xtreme Cars", True, (255, 255, 255))
        title_y = resolution_manager.get_scaled_spacing(50)
        title_x = (actual_width - title.get_width()) // 2
        
        print(f"    Title: pos=({title_x}, {title_y}), size={title.get_width()}x{title.get_height()}px")
        
        # Render the main menu
        background.draw(game.screen)
        game.screen.blit(title, (title_x, title_y))
        
        for button in buttons:
            button.draw(game.screen)
        
        # Save screenshot
        screenshot_filename = f"main_menu_{resolution_name.replace('x', '_').replace(' ', '_').lower()}.png"
        pygame.image.save(game.screen, screenshot_filename)
        print(f"  💾 Screenshot saved: {screenshot_filename}")
        
        pygame.quit()
        
        return {
            'resolution': f"{actual_width}x{actual_height}",
            'button_font_size': button_font_size,
            'button_size': f"{button_width}x{button_height}",
            'button_spacing': button_spacing,
            'title_font_size': title_font_size,
            'title_size': f"{title.get_width()}x{title.get_height()}",
            'first_button_pos': f"({buttons[0].x}, {buttons[0].y})",
            'screenshot': screenshot_filename
        }
        
    except Exception as e:
        print(f"  ❌ Error capturing {resolution_name}: {e}")
        pygame.quit()
        return None


def create_scaling_comparison():
    """Create a comprehensive scaling comparison"""
    print("🎯 MAIN MENU SCALING DEMONSTRATION")
    print("=" * 50)
    
    # Test resolutions
    test_resolutions = [
        (1024, 768, "1024x768_Small"),
        (1366, 768, "1366x768_Medium"),
        (1920, 1080, "1920x1080_Large")
    ]
    
    results = []
    
    for width, height, name in test_resolutions:
        result = capture_main_menu_at_resolution(width, height, name)
        if result:
            results.append((name, result))
        time.sleep(0.5)  # Brief pause between captures
    
    # Create comparison table
    print("\n📊 SCALING COMPARISON TABLE")
    print("=" * 80)
    print(f"{'Resolution':<15} {'Button Font':<12} {'Button Size':<12} {'Spacing':<10} {'Title Font':<12}")
    print("-" * 80)
    
    for name, data in results:
        print(f"{data['resolution']:<15} {data['button_font_size']}px{'':<7} {data['button_size']:<12} {data['button_spacing']}px{'':<6} {data['title_font_size']}px")
    
    # Calculate scaling ratios
    if len(results) >= 2:
        small_res = results[0][1]  # 1024x768
        large_res = results[-1][1]  # 1920x1080
        
        print(f"\n📈 SCALING RATIOS (1920x1080 vs 1024x768):")
        print("-" * 50)
        
        font_ratio = large_res['button_font_size'] / small_res['button_font_size']
        spacing_ratio = large_res['button_spacing'] / small_res['button_spacing']
        title_ratio = large_res['title_font_size'] / small_res['title_font_size']
        
        print(f"Button font scaling: {small_res['button_font_size']}px → {large_res['button_font_size']}px ({font_ratio:.2f}x)")
        print(f"Button spacing scaling: {small_res['button_spacing']}px → {large_res['button_spacing']}px ({spacing_ratio:.2f}x)")
        print(f"Title font scaling: {small_res['title_font_size']}px → {large_res['title_font_size']}px ({title_ratio:.2f}x)")
    
    print(f"\n💡 WHAT YOU SHOULD SEE:")
    print("-" * 30)
    print("✅ Buttons should be SMALLER at 1024x768 and LARGER at 1920x1080")
    print("✅ Text should be MORE READABLE (larger) at higher resolutions")
    print("✅ Spacing should be TIGHTER at low res, LOOSER at high res")
    print("✅ Everything should remain CENTERED and PROPORTIONAL")
    
    print(f"\n📁 Screenshots saved for visual comparison:")
    for name, data in results:
        print(f"  • {data['screenshot']}")
    
    return results


def test_live_scaling_changes():
    """Test scaling changes in real-time"""
    print("\n🔄 LIVE SCALING TEST")
    print("=" * 30)
    print("This will show scaling changes in real-time...")
    print("Press any key to cycle through resolutions, ESC to exit")
    
    pygame.init()
    pygame.font.init()
    
    test_resolutions = [
        (1024, 768, "1024x768"),
        (1280, 720, "1280x720"),
        (1920, 1080, "1920x1080")
    ]
    
    current_res_index = 0
    game = Game()
    clock = pygame.time.Clock()
    
    while True:
        # Set current resolution
        width, height, name = test_resolutions[current_res_index]
        game.change_resolution(width, height)
        
        # Get scaling info
        resolution_manager = get_resolution_manager()
        button_font_size = resolution_manager.get_scaled_font_size(36)
        button_width, button_height = resolution_manager.get_scaled_button_size(250, 60)
        
        # Display info
        print(f"\n📺 Current: {name} - Font: {button_font_size}px, Button: {button_width}x{button_height}px")
        
        # Simple render loop
        running = True
        start_time = time.time()
        
        while running and (time.time() - start_time) < 3:  # Show for 3 seconds
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    return
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        pygame.quit()
                        return
                    running = False  # Move to next resolution
            
            # Render main menu
            background = Background('background', game.s_width, game.s_height)
            background.draw(game.screen)
            
            # Show resolution info on screen
            info_font = load_font("arial", 24)
            info_text = info_font.render(f"{name} - Font: {button_font_size}px", True, (255, 255, 0))
            game.screen.blit(info_text, (10, 10))
            
            pygame.display.update()
            clock.tick(60)
        
        # Move to next resolution
        current_res_index = (current_res_index + 1) % len(test_resolutions)
        if current_res_index == 0:  # Completed full cycle
            break
    
    pygame.quit()
    print("🏁 Live scaling test completed!")


if __name__ == "__main__":
    print("🎮 MAIN MENU SCALING VERIFICATION TOOL")
    print("=" * 40)
    
    try:
        # Create visual comparison
        results = create_scaling_comparison()
        
        # Ask for live test
        response = input("\n🔄 Run live scaling test? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            test_live_scaling_changes()
        
        print("\n✅ VERIFICATION COMPLETE!")
        print("Check the generated screenshots to see the scaling differences.")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()
